'use client'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html>
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <h2 className="text-2xl font-bold mb-4"><PERSON>ir şeyler yanlış gitti!</h2>
          <p className="mb-4 text-red-600">{error.message}</p>
          <button
            onClick={() => reset()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            <PERSON><PERSON><PERSON> dene
          </button>
        </div>
      </body>
    </html>
  )
}