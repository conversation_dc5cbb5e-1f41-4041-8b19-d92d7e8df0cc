'use client'

import { useEffect } from 'react'
import Button from '@/components/ui/Button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Page error:', error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <h2 className="text-xl font-semibold mb-4">Bir hata olu<PERSON></h2>
      <p className="mb-4 text-red-600">{error.message}</p>
      <Button 
        variant="primary" 
        onClick={() => reset()}
      >
        Te<PERSON><PERSON> dene
      </Button>
    </div>
  )
}