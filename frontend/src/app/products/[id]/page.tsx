'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DetailPage, { DetailSection, DetailField, DetailGrid } from '@/components/ui/DetailPage';
import Button from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Product, Category, Campaign } from '@/types';
import { productService } from '@/services/productService';
import { categoryService } from '@/services/categoryService';
import { campaignService } from '@/services/campaignService';
import { formatDate } from '@/lib/utils';
import { Edit, Trash2, Package, Hash, DollarSign, Archive, Tag, Megaphone } from 'lucide-react';

export default function ProductDetailPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [product, setProduct] = useState<Product | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProduct();
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadProduct = async () => {
    try {
      setLoading(true);
      setError(null);

      const productData = await productService.getById(id);
      if (!productData) {
        setError('Ürün bulunamadı.');
        return;
      }
      setProduct(productData);

      // Load category information
      if (productData.category_id) {
        const categoryData = await categoryService.getById(productData.category_id);
        if (categoryData) {
          setCategory(categoryData);
        }
      }

      // Load campaign information if exists and is not empty UUID
      if (productData.campaign_id && productData.campaign_id !== '00000000-0000-0000-0000-000000000000') {
        const campaignData = await campaignService.getById(productData.campaign_id);
        if (campaignData) {
          setCampaign(campaignData);
        }
      }
    } catch (error) {
      console.error('Error loading product:', error);
      setError('Ürün bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = () => {
    if (!product) return null;
    
    if (product.quantity === 0) {
      return <Badge variant="danger">Stokta Yok</Badge>;
    } else if (product.quantity <= 5) {
      return <Badge variant="warning">Düşük Stok</Badge>;
    } else {
      return <Badge variant="success">Stokta Var</Badge>;
    }
  };

  const hasDiscount = product && product.discounted_price < product.price;

  const actions = product && (
    <>
      <Button
        variant="info"
        onClick={() => {/* TODO: Edit modal */}}
        className="flex items-center"
      >
        <Edit className="h-4 w-4 mr-2" />
        Düzenle
      </Button>
      <Button
        variant="danger"
        onClick={() => {/* TODO: Delete confirmation */}}
        className="flex items-center"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Sil
      </Button>
    </>
  );

  return (
    <DetailPage
      title={product?.name || 'Ürün Detayı'}
      subtitle="Ürün bilgilerini görüntüleyin"
      loading={loading}
      error={error}
      backUrl="/products"
      actions={actions}
    >
      {product && (
        <DetailGrid columns={2}>
          {/* Genel Bilgiler */}
          <DetailSection title="Genel Bilgiler">
            <div className="space-y-3">
              <DetailField 
                label="Ürün Adı" 
                value={
                  <div className="flex items-center">
                    <Package className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-medium">{product.name}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Ürün Kodu" 
                value={
                  <div className="flex items-center">
                    <Hash className="h-4 w-4 text-gray-600 mr-2" />
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{product.product_code}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Kategori" 
                value={
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 text-purple-600 mr-2" />
                    <span>{category?.name || 'Kategori bulunamadı'}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Stok Durumu" 
                value={getStockStatus()} 
              />
            </div>
          </DetailSection>

          {/* Fiyat ve Stok Bilgileri */}
          <DetailSection title="Fiyat ve Stok">
            <div className="space-y-3">
              <DetailField 
                label="Orijinal Fiyat" 
                value={
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-green-600 mr-2" />
                    <span className={hasDiscount ? 'line-through text-gray-500' : 'font-semibold text-green-600'}>
                      ₺{product.price.toLocaleString('tr-TR')}
                    </span>
                  </div>
                } 
              />
              {hasDiscount && (
                <DetailField 
                  label="İndirimli Fiyat" 
                  value={
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-red-600 mr-2" />
                      <span className="font-semibold text-red-600">
                        ₺{product.discounted_price.toLocaleString('tr-TR')}
                      </span>
                    </div>
                  } 
                />
              )}
              <DetailField 
                label="Stok Miktarı" 
                value={
                  <div className="flex items-center">
                    <Archive className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-semibold">{product.quantity} adet</span>
                  </div>
                } 
              />
            </div>
          </DetailSection>

          {/* Kampanya Bilgileri */}
          {campaign && (
            <DetailSection title="Kampanya Bilgileri" className="md:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <DetailField 
                  label="Kampanya Adı" 
                  value={
                    <div className="flex items-center">
                      <Megaphone className="h-4 w-4 text-orange-600 mr-2" />
                      <span className="font-medium">{campaign.name}</span>
                    </div>
                  } 
                />
                <DetailField 
                  label="İndirim Türü" 
                  value={campaign.discount_type === 'percentage' ? 'Yüzde' : 'Sabit Tutar'} 
                />
                <DetailField 
                  label="İndirim Miktarı" 
                  value={
                    campaign.discount_type === 'percentage' 
                      ? `%${campaign.discount_percent}`
                      : `₺${campaign.discount_amount.toLocaleString('tr-TR')}`
                  } 
                />
              </div>
              
              {hasDiscount && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="text-sm text-orange-800">
                    <strong>Kampanya Aktif:</strong> Bu ürün şu anda &quot;{campaign.name}&quot; kampanyası kapsamında indirimli satılmaktadır.
                  </div>
                </div>
              )}
            </DetailSection>
          )}

          {/* Tarih Bilgileri */}
          <DetailSection title="Tarih Bilgileri" className={campaign ? '' : 'md:col-span-2'}>
            <div className="space-y-3">
              <DetailField label="Oluşturulma" value={formatDate(product.created_at)} />
              <DetailField label="Son Güncelleme" value={formatDate(product.updated_at)} />
            </div>
          </DetailSection>
        </DetailGrid>
      )}
    </DetailPage>
  );
}
