'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useCurrentUserOrganization } from '@/hooks/useAppState';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, Package, Search, Eye, FileSpreadsheet, PackagePlus } from 'lucide-react';
import { productService } from '@/services/productService';
import { categoryService } from '@/services/categoryService';
import { campaignService } from '@/services/campaignService';
import { Product, CreateProductRequest, UpdateProductRequest, Category, Campaign, PaginationRequest } from '@/types';
import ExcelImportModal from '@/components/products/ExcelImportModal';
import StockAddModal from '@/components/products/StockAddModal';

export default function ProductsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const currentOrganizationID = useCurrentUserOrganization();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isExcelImportModalOpen, setIsExcelImportModalOpen] = useState(false);
  const [isStockAddModalOpen, setIsStockAddModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<CreateProductRequest>({
    name: '',
    product_code: '',
    price: 0,
    quantity: 0,
    category_id: '',
    campaign_id: '',
  });

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState(''); // Arama input'u için ayrı state
  const [stockFilter, setStockFilter] = useState<'all' | 'in_stock' | 'low' | 'out'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [lowStockThreshold] = useState(5); // Düşük stok eşiği
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsData, categoriesData, campaignsData] = await Promise.all([
        productService.getAll(), // Tüm ürünleri al
        categoryService.getAll(),
        campaignService.getAll(),
      ]);
      setProducts(productsData);
      setCategories(categoriesData);
      setCampaigns(campaignsData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Reset pagination when filters change
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleStockFilterChange = (value: 'all' | 'in_stock' | 'low' | 'out') => {
    setStockFilter(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCategoryFilterChange = (value: string) => {
    setCategoryFilter(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setSearchInput('');
    setStockFilter('all');
    setCategoryFilter('all');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleOpenCreateModal = () => {
    // Kategori kontrolü
    if (categories.length === 0) {
      alert('Ürün oluşturmadan önce en az bir kategori oluşturmanız gerekiyor. Lütfen önce Kategoriler sayfasından kategori ekleyin.');
      router.push('/categories');
      return;
    }
    setIsCreateModalOpen(true);
  };

  const handleCreate = async () => {
    try {
      const productData = {
        ...formData,
        organization_id: currentOrganizationID || user?.organization_id || ''
      };
      await productService.create(productData);
      setIsCreateModalOpen(false);
      setFormData({
        name: '',
        product_code: '',
        price: 0,
        quantity: 0,
        category_id: '',
        campaign_id: '',
      });
      loadData();
    } catch (error) {
      console.error('Error creating product:', error);
    }
  };

  const handleEdit = async () => {
    if (!editingProduct) return;
    
    try {
      const updateData: UpdateProductRequest = {
        name: formData.name,
        product_code: formData.product_code,
        price: formData.price,
        quantity: formData.quantity,
        category_id: formData.category_id,
        campaign_id: formData.campaign_id,
      };
      
      await productService.update(editingProduct.id, updateData);
      setIsEditModalOpen(false);
      setEditingProduct(null);
      loadData();
    } catch (error) {
      console.error('Error updating product:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu ürünü silmek istediğinizden emin misiniz?')) return;
    
    try {
      await productService.delete(id);
      loadData();
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  };

  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      product_code: product.product_code,
      price: product.price,
      quantity: product.quantity,
      category_id: product.category_id,
      campaign_id: product.campaign_id,
    });
    setIsEditModalOpen(true);
  };

  const getCategoryName = (categoryId: string | number) => {
    const category = categories.find(c => c.id === categoryId.toString());
    return category?.name || 'Bilinmeyen';
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    // Search filter with Turkish character normalization
    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    const normalizedProductName = normalizeTurkish(product.name);
    const normalizedProductCode = normalizeTurkish(product.product_code);

    const matchesSearch = normalizedProductName.includes(normalizedSearchTerm) ||
                         normalizedProductCode.includes(normalizedSearchTerm);

    // Stock filter
    let matchesStock = true;
    if (stockFilter === 'in_stock') {
      matchesStock = product.quantity > 0;
    } else if (stockFilter === 'low') {
      matchesStock = product.quantity > 0 && product.quantity <= lowStockThreshold;
    } else if (stockFilter === 'out') {
      matchesStock = product.quantity === 0;
    }

    // Category filter
    const matchesCategory = categoryFilter === 'all' || product.category_id.toString() === categoryFilter;

    return matchesSearch && matchesStock && matchesCategory;
  });

  // Get stock status for a product
  const getStockStatus = (quantity: number) => {
    if (quantity === 0) {
      return { status: 'out', color: 'text-red-600', bgColor: 'bg-red-100', text: 'Tükendi' };
    } else if (quantity <= lowStockThreshold) {
      return { status: 'low', color: 'text-yellow-600', bgColor: 'bg-yellow-100', text: 'Düşük Stok' };
    } else {
      return { status: 'good', color: 'text-green-600', bgColor: 'bg-green-100', text: 'Stokta' };
    }
  };

  const columns: Column<Product>[] = [
    {
      key: 'product_code',
      header: 'Kod',
      className: 'w-24',
      render: (product) => (
        <div className="font-medium text-gray-900 text-sm">{product.product_code}</div>
      ),
    },
    {
      key: 'name',
      header: 'Ürün Adı',
      className: 'w-48',
      render: (product) => (
        <div className="flex items-center">
          <Package className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
          <div className="font-medium text-gray-900 truncate" title={product.name}>
            {product.name.length > 25 ? `${product.name.substring(0, 25)}...` : product.name}
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      header: 'Kategori',
      className: 'w-32',
      render: (product) => {
        const category = categories.find(c => c.id.toString() === product.category_id.toString());
        const categoryName = category?.name || 'Bilinmeyen';
        return (
          <div className="text-sm text-gray-700 truncate" title={categoryName}>
            {categoryName.length > 15 ? `${categoryName.substring(0, 15)}...` : categoryName}
          </div>
        );
      },
    },
    {
      key: 'price',
      header: 'Fiyat',
      className: 'w-28',
      render: (product) => (
        <div>
          <div className="font-medium text-sm">{formatPrice(product.price)}</div>
          {product.discounted_price && product.discounted_price !== product.price && (
            <div className="text-xs text-green-600">{formatPrice(product.discounted_price)}</div>
          )}
        </div>
      ),
    },
    {
      key: 'quantity',
      header: 'Stok',
      className: 'w-24',
      render: (product) => {
        const stockStatus = getStockStatus(product.quantity);
        return (
          <div className="flex items-center">
            <span className="font-medium text-sm mr-1">{product.quantity}</span>
            <span className={`px-1 py-0.5 rounded text-xs ${stockStatus.bgColor} ${stockStatus.color}`}>
              {stockStatus.text}
            </span>
          </div>
        );
      },
    },
    {
      key: 'campaign',
      header: 'Kampanya',
      className: 'w-20',
      render: (product) => {
        if (product.campaign_id && product.campaign_id !== '0') {
          const campaign = campaigns.find(c => c.id.toString() === product.campaign_id.toString());
          const campaignName = campaign?.name || 'Bulunamadı';
          return (
            <span className="text-green-600 text-xs truncate block" title={campaignName}>
              {campaignName.length > 8 ? `${campaignName.substring(0, 8)}...` : campaignName}
            </span>
          );
        }
        return <span className="text-gray-400 text-xs">-</span>;
      },
    },
    {
      key: 'actions',
      header: 'İşlemler',
      className: 'text-right w-32',
      render: (product) => (
        <div className="flex justify-end space-x-1">
          <Button
            size="sm"
            variant="success"
            onClick={() => router.push(`/products/${product.id}`)}
            title="Detay"
          >
            <Eye className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="info"
            onClick={() => openEditModal(product)}
            title="Düzenle"
          >
            <Edit className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(product.id)}
            title="Sil"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Ürünler</h1>
            <p className="text-gray-600">Ürün stokunuzu yönetin</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              onClick={() => setIsStockAddModalOpen(true)}
            >
              <PackagePlus className="h-4 w-4 mr-2" />
              Stok Ekle
            </Button>
            <Button
              variant="secondary"
              onClick={() => setIsExcelImportModalOpen(true)}
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Excel ile Yükle
            </Button>
            <Button onClick={handleOpenCreateModal}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Ürün
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="md:col-span-2">
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Ürün adı veya kod ara..."
                    value={searchInput}
                    onChange={(e) => handleSearchInputChange(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Button
                  onClick={handleSearch}
                  variant="primary"
                  className="px-4 py-2"
                >
                  Ara
                </Button>
                {(searchTerm || stockFilter !== 'all' || categoryFilter !== 'all') && (
                  <Button
                    onClick={handleClearFilters}
                    variant="secondary"
                    className="px-4 py-2"
                  >
                    Temizle
                  </Button>
                )}
              </div>
            </div>

            {/* Stock Filter */}
            <div>
              <select
                value={stockFilter}
                onChange={(e) => handleStockFilterChange(e.target.value as 'all' | 'in_stock' | 'low' | 'out')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Tüm Stoklar</option>
                <option value="in_stock">Sadece Stokta Olanlar</option>
                <option value="low">Düşük Stok</option>
                <option value="out">Tükenen</option>
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <select
                value={categoryFilter}
                onChange={(e) => handleCategoryFilterChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Tüm Kategoriler</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Filter Summary */}
          <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
            <div>
              {filteredProducts.length} ürün gösteriliyor
              {searchTerm && ` • "${searchTerm}" araması`}
              {stockFilter !== 'all' && ` • ${
                stockFilter === 'in_stock' ? 'Sadece stokta olanlar' :
                stockFilter === 'low' ? 'Düşük stok' : 'Tükenen'
              } filtresi`}
              {categoryFilter !== 'all' && ` • ${getCategoryName(categoryFilter)} kategorisi`}
            </div>
            {(searchTerm || stockFilter !== 'all' || categoryFilter !== 'all') && (
              <button
                onClick={handleClearFilters}
                className="text-blue-600 hover:text-blue-800"
              >
                Filtreleri Temizle
              </button>
            )}
          </div>
        </div>

        {/* Products Display */}
        <div className="overflow-x-auto">
          <DataTable
            columns={columns}
            data={filteredProducts}
            pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredProducts.length, total_pages: Math.ceil(filteredProducts.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredProducts.length / pagination.per_page), has_prev: pagination.page > 1 }}
            onPageChange={handlePageChange}
            onPerPageChange={handlePerPageChange}
            loading={loading}
            emptyMessage="Henüz ürün kaydı bulunmuyor"
            emptyIcon={<Package className="h-12 w-12 text-gray-400" />}
            useClientPagination={true}
            className="min-w-full"
          />
        </div>


        {/* Create Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Yeni Ürün Ekle"
        >
          <div className="space-y-4">
            <Input
              label="Ürün Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Ürün adını girin"
            />
            <Input
              label="Ürün Kodu"
              value={formData.product_code}
              onChange={(e) => setFormData({ ...formData, product_code: e.target.value })}
              placeholder="Ürün kodu (örn: SAM-BUZ-001)"
            />
            <Input
              label="Fiyat"
              type="number"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
            />
            <Input
              label="Stok Miktarı"
              type="number"
              value={formData.quantity}
              onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) || 0 })}
              placeholder="0"
            />
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kategori
              </label>
              <select
                value={formData.category_id}
                onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Kategori seçin</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kampanya (Opsiyonel)
              </label>
              <select
                value={formData.campaign_id}
                onChange={(e) => setFormData({ ...formData, campaign_id: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Kampanya yok</option>
                {campaigns.map((campaign) => (
                  <option key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleCreate}>
                Ürün Ekle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Ürün Düzenle"
        >
          <div className="space-y-4">
            <Input
              label="Ürün Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Ürün adını girin"
            />
            <Input
              label="Ürün Kodu"
              value={formData.product_code}
              onChange={(e) => setFormData({ ...formData, product_code: e.target.value })}
              placeholder="Ürün kodu (örn: SAM-BUZ-001)"
            />
            <Input
              label="Fiyat"
              type="number"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
            />
            <Input
              label="Stok Miktarı"
              type="number"
              value={formData.quantity}
              onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) || 0 })}
              placeholder="0"
            />
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kategori
              </label>
              <select
                value={formData.category_id}
                onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="0">Kategori seçin</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kampanya (Opsiyonel)
              </label>
              <select
                value={formData.campaign_id}
                onChange={(e) => setFormData({ ...formData, campaign_id: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="0">Kampanya yok</option>
                {campaigns.map((campaign) => (
                  <option key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleEdit}>
                Güncelle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Stock Add Modal */}
        <StockAddModal
          isOpen={isStockAddModalOpen}
          onClose={() => setIsStockAddModalOpen(false)}
          onSuccess={loadData}
        />

        {/* Excel Import Modal */}
        <ExcelImportModal
          isOpen={isExcelImportModalOpen}
          onClose={() => setIsExcelImportModalOpen(false)}
          onSuccess={loadData}
        />
      </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
