'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, Folder, Search } from 'lucide-react';
import { categoryService } from '@/services/categoryService';
import { Category, CreateCategoryRequest, UpdateCategoryRequest, PaginationRequest } from '@/types';
import { useAuth } from '@/contexts/AuthContext';

export default function CategoriesPage() {
  const { user } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CreateCategoryRequest>({
    name: '',
  });
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  useEffect(() => {
    loadCategories();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await categoryService.getAll();
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreate = async () => {
    try {
      const categoryData = {
        ...formData,
        organization_id: user?.organization_id || ''
      };
      await categoryService.create(categoryData);
      setIsCreateModalOpen(false);
      setFormData({ name: '' });
      loadCategories();
    } catch (error) {
      console.error('Error creating category:', error);
    }
  };

  const handleEdit = async () => {
    if (!editingCategory) return;
    
    try {
      const updateData: UpdateCategoryRequest = {
        name: formData.name,
      };
      
      await categoryService.update(editingCategory.id, updateData);
      setIsEditModalOpen(false);
      setEditingCategory(null);
      loadCategories();
    } catch (error) {
      console.error('Error updating category:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) return;
    
    try {
      await categoryService.delete(id);
      loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  const openEditModal = (category: Category) => {
    setEditingCategory(category);
    setFormData({ name: category.name });
    setIsEditModalOpen(true);
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter categories based on search term
  const filteredCategories = categories.filter(category => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return normalizeTurkish(category.name).includes(normalizedSearchTerm);
  });

  const columns: Column<Category>[] = [
    {
      key: 'name',
      header: 'Kategori',
      render: (category) => (
        <div className="flex items-center">
          <Folder className="h-5 w-5 text-blue-600 mr-2" />
          <div className="font-medium text-gray-900">{category.name}</div>
        </div>
      ),
    },
    {
      key: 'created_at',
      header: 'Oluşturulma Tarihi',
      render: (category) => (
        <div className="text-sm text-gray-500">
          {new Date(category.created_at).toLocaleDateString('tr-TR')}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      render: (category) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => openEditModal(category)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(category.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Kategoriler</h1>
            <p className="text-gray-600">Ürün kategorilerinizi yönetin</p>
          </div>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Kategori
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Kategori adı ara..."
                value={searchInput}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="primary"
              className="px-4 py-2"
            >
              Ara
            </Button>
            {(searchTerm) && (
              <Button
                onClick={handleClearSearch}
                variant="secondary"
                className="px-4 py-2"
              >
                Temizle
              </Button>
            )}
          </div>

          {/* Search Summary */}
          {searchTerm && (
            <div className="mt-4 text-sm text-gray-600">
              {filteredCategories.length} kategori gösteriliyor • &quot;{searchTerm}&quot; araması
            </div>
          )}
        </div>

        {/* Categories Display */}
        <DataTable
          columns={columns}
          data={filteredCategories}
          pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredCategories.length, total_pages: Math.ceil(filteredCategories.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredCategories.length / pagination.per_page), has_prev: pagination.page > 1 }}
          onPageChange={handlePageChange}
          onPerPageChange={handlePerPageChange}
          loading={loading}
          emptyMessage="Henüz kategori kaydı bulunmuyor"
          emptyIcon={<Folder className="h-12 w-12 text-gray-400" />}
          useClientPagination={true}
        />

        {/* Create Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Yeni Kategori Ekle"
        >
          <div className="space-y-4">
            <Input
              label="Kategori Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Kategori adını girin"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleCreate}>
                Kategori Ekle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Kategori Düzenle"
        >
          <div className="space-y-4">
            <Input
              label="Kategori Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Kategori adını girin"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleEdit}>
                Güncelle
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}
