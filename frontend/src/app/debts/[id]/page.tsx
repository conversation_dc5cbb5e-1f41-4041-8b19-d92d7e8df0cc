'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DetailPage, { DetailSection, DetailField, DetailGrid } from '@/components/ui/DetailPage';
import Button from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Debt } from '@/types';
import { debtService } from '@/services/debtService';
import { formatDate, formatPhone } from '@/lib/utils';
import { Edit, Trash2, CreditCard, Users, Phone, DollarSign, Calendar, Receipt, AlertCircle } from 'lucide-react';

export default function DebtDetailPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [debt, setDebt] = useState<Debt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDebt();
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadDebt = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await debtService.getById(id);
      if (!data) {
        setError('Borç bulunamadı.');
        return;
      }
      setDebt(data);
    } catch (error) {
      console.error('Error loading debt:', error);
      setError('Borç bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };



  const getPaymentStatus = () => {
    if (!debt) return null;
    
    return debt.is_paid ? (
      <Badge variant="success">Ödendi</Badge>
    ) : (
      <Badge variant="danger">Ödenmedi</Badge>
    );
  };

  const getFullName = () => {
    if (!debt) return '';
    return `${debt.name} ${debt.surname}`.trim();
  };

  const actions = debt && (
    <>
      {!debt.is_paid && (
        <Button
          variant="warning"
          onClick={() => {/* TODO: Pay modal */}}
          className="flex items-center"
        >
          <CreditCard className="h-4 w-4 mr-2" />
          Ödeme Yap
        </Button>
      )}
      <Button
        variant="info"
        onClick={() => {/* TODO: Edit modal */}}
        className="flex items-center"
      >
        <Edit className="h-4 w-4 mr-2" />
        Düzenle
      </Button>
      <Button
        variant="danger"
        onClick={() => {/* TODO: Delete confirmation */}}
        className="flex items-center"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Sil
      </Button>
    </>
  );

  return (
    <DetailPage
      title={`Borç #${debt?.id.slice(-8) || 'Detayı'}`}
      subtitle="Borç bilgilerini görüntüleyin"
      loading={loading}
      error={error}
      backUrl="/debts"
      actions={actions}
    >
      {debt && (
        <DetailGrid columns={2}>
          {/* Borçlu Bilgileri */}
          <DetailSection title="Borçlu Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Ad Soyad" 
                value={
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-medium">{getFullName()}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Ad" 
                value={debt.name} 
              />
              <DetailField 
                label="Soyad" 
                value={debt.surname} 
              />
              <DetailField 
                label="Telefon" 
                value={
                  debt.phone ? (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-purple-600 mr-2" />
                      <a 
                        href={`tel:${debt.phone}`}
                        className="text-purple-600 hover:text-purple-800 font-medium"
                      >
                        {formatPhone(debt.phone)}
                      </a>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
            </div>
          </DetailSection>

          {/* Borç Bilgileri */}
          <DetailSection title="Borç Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Borç Tutarı" 
                value={
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-red-600 mr-2" />
                    <span className="font-bold text-red-600 text-lg">
                      ₺{debt.amount.toLocaleString('tr-TR')}
                    </span>
                  </div>
                } 
              />
              <DetailField 
                label="Ödeme Durumu" 
                value={getPaymentStatus()} 
              />
              <DetailField 
                label="Oluşturulma" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-green-600 mr-2" />
                    <span>{formatDate(debt.created_at)}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Son Güncelleme" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                    <span>{formatDate(debt.updated_at)}</span>
                  </div>
                } 
              />
            </div>
          </DetailSection>

          {/* Borç Durumu */}
          <DetailSection title="Borç Durumu" className="md:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`text-center p-4 rounded-lg ${debt.is_paid ? 'bg-green-50' : 'bg-red-50'}`}>
                <div className="text-sm text-gray-600 mb-1">Durum</div>
                <div className={`text-lg font-semibold ${debt.is_paid ? 'text-green-600' : 'text-red-600'}`}>
                  {debt.is_paid ? 'Ödendi' : 'Ödenmedi'}
                </div>
              </div>
              
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Borç Tutarı</div>
                <div className="text-lg font-semibold text-blue-600">
                  ₺{debt.amount.toLocaleString('tr-TR')}
                </div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Borçlu</div>
                <div className="text-lg font-semibold text-gray-700">
                  {getFullName()}
                </div>
              </div>
            </div>
            
            {!debt.is_paid && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <strong>Ödeme Bekliyor:</strong> Bu borç henüz ödenmemiştir. 
                    Ödeme yapıldığında otomatik olarak kasaya eklenecektir.
                  </div>
                </div>
              </div>
            )}
            
            {debt.is_paid && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start">
                  <CreditCard className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-green-800">
                    <strong>Ödeme Tamamlandı:</strong> Bu borç başarıyla ödenmiştir. 
                    Ödeme tutarı kasaya eklenmiştir.
                  </div>
                </div>
              </div>
            )}
          </DetailSection>

          {/* Borç Özeti */}
          <DetailSection title="Borç Özeti" className="md:col-span-2">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Receipt className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="font-medium text-gray-900">Borç Fişi</h3>
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <p><strong>Borçlu:</strong> {getFullName()}</p>
                <p><strong>Telefon:</strong> {debt.phone ? formatPhone(debt.phone) : 'Belirtilmemiş'}</p>
                <p><strong>Tutar:</strong> ₺{debt.amount.toLocaleString('tr-TR')}</p>
                <p><strong>Durum:</strong> {debt.is_paid ? 'Ödendi' : 'Ödenmedi'}</p>
                <p><strong>Kayıt Tarihi:</strong> {formatDate(debt.created_at)}</p>
                {debt.is_paid && (
                  <p><strong>Ödeme Tarihi:</strong> {formatDate(debt.updated_at)}</p>
                )}
              </div>
            </div>
          </DetailSection>
        </DetailGrid>
      )}
    </DetailPage>
  );
}
