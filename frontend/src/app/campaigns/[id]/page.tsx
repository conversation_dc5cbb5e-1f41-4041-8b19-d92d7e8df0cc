'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DetailPage, { DetailSection, DetailField, DetailGrid } from '@/components/ui/DetailPage';
import Button from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Campaign } from '@/types';
import { campaignService } from '@/services/campaignService';
import { formatDate } from '@/lib/utils';
import { Edit, Trash2, Tag, Calendar, Percent, DollarSign, Gift } from 'lucide-react';

export default function CampaignDetailPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCampaign();
  }, [id]);

  const loadCampaign = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await campaignService.getById(id);
      if (!data) {
        setError('Kampanya bulunamadı.');
        return;
      }
      setCampaign(data);
    } catch (error) {
      console.error('Error loading campaign:', error);
      setError('Kampanya bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const isActive = campaign && new Date(campaign.start_date) <= new Date() && new Date(campaign.end_date) >= new Date();
  const isExpired = campaign && new Date(campaign.end_date) < new Date();
  const isUpcoming = campaign && new Date(campaign.start_date) > new Date();

  const getStatusBadge = () => {
    if (isActive) {
      return <Badge variant="success">Aktif</Badge>;
    } else if (isExpired) {
      return <Badge variant="danger">Süresi Dolmuş</Badge>;
    } else if (isUpcoming) {
      return <Badge variant="warning">Yaklaşan</Badge>;
    }
    return <Badge variant="secondary">Bilinmiyor</Badge>;
  };

  const getDiscountDisplay = () => {
    if (!campaign) return '-';
    
    if (campaign.discount_type === 'percentage') {
      return (
        <div className="flex items-center">
          <Percent className="h-4 w-4 text-blue-600 mr-1" />
          <span className="font-semibold text-blue-600">%{campaign.discount_percent}</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center">
          <DollarSign className="h-4 w-4 text-green-600 mr-1" />
          <span className="font-semibold text-green-600">₺{campaign.discount_amount.toLocaleString('tr-TR')}</span>
        </div>
      );
    }
  };

  const actions = campaign && (
    <>
      <Button
        variant="info"
        onClick={() => {/* TODO: Edit modal */}}
        className="flex items-center"
      >
        <Edit className="h-4 w-4 mr-2" />
        Düzenle
      </Button>
      <Button
        variant="danger"
        onClick={() => {/* TODO: Delete confirmation */}}
        className="flex items-center"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Sil
      </Button>
    </>
  );

  return (
    <DetailPage
      title={campaign?.name || 'Kampanya Detayı'}
      subtitle="Kampanya bilgilerini görüntüleyin"
      loading={loading}
      error={error}
      backUrl="/campaigns"
      actions={actions}
    >
      {campaign && (
        <DetailGrid columns={2}>
          {/* Genel Bilgiler */}
          <DetailSection title="Genel Bilgiler">
            <div className="space-y-3">
              <DetailField 
                label="Kampanya Adı" 
                value={
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-medium">{campaign.name}</span>
                  </div>
                } 
              />
              <DetailField label="Açıklama" value={campaign.description || '-'} />
              <DetailField label="Durum" value={getStatusBadge()} />
              <DetailField 
                label="Cashback" 
                value={
                  <div className="flex items-center">
                    <Gift className={`h-4 w-4 mr-1 ${campaign.is_cashback ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className={campaign.is_cashback ? 'text-green-600 font-medium' : 'text-gray-500'}>
                      {campaign.is_cashback ? 'Aktif' : 'Pasif'}
                    </span>
                  </div>
                } 
              />
            </div>
          </DetailSection>

          {/* Tarih Bilgileri */}
          <DetailSection title="Tarih Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Başlangıç Tarihi" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-green-600 mr-2" />
                    <span>{formatDate(campaign.start_date)}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Bitiş Tarihi" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-red-600 mr-2" />
                    <span>{formatDate(campaign.end_date)}</span>
                  </div>
                } 
              />
              <DetailField label="Oluşturulma" value={formatDate(campaign.created_at)} />
              <DetailField label="Son Güncelleme" value={formatDate(campaign.updated_at)} />
            </div>
          </DetailSection>

          {/* İndirim Bilgileri */}
          <DetailSection title="İndirim Bilgileri" className="md:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">İndirim Türü</div>
                <div className="text-lg font-semibold text-blue-600">
                  {campaign.discount_type === 'percentage' ? 'Yüzde' : 'Sabit Tutar'}
                </div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">İndirim Miktarı</div>
                <div className="text-lg font-semibold">
                  {getDiscountDisplay()}
                </div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Cashback Durumu</div>
                <div className={`text-lg font-semibold ${campaign.is_cashback ? 'text-green-600' : 'text-gray-500'}`}>
                  {campaign.is_cashback ? 'Aktif' : 'Pasif'}
                </div>
              </div>
            </div>
            
            {campaign.is_cashback && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="text-sm text-yellow-800">
                  <strong>Cashback Açıklaması:</strong> Bu kampanyada verilen indirim tutarı ana organizasyon tarafından geri ödenir. 
                  Ürünün tam fiyatı kasaya eklenir, ancak satıcı ek manuel indirim yaparsa, kasa orijinal fiyat eksi manuel indirim tutarını alır.
                </div>
              </div>
            )}
          </DetailSection>
        </DetailGrid>
      )}
    </DetailPage>
  );
}
