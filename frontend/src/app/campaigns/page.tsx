'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, Tag, Percent, DollarSign, Search, Eye } from 'lucide-react';
import { campaignService } from '@/services/campaignService';
import { Campaign, CreateCampaignRequest, UpdateCampaignRequest, PaginationRequest, PaginatedResponse } from '@/types';

export default function CampaignsPage() {
  const router = useRouter();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [filter, setFilter] = useState<'all' | 'active'>('all');
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  const [formData, setFormData] = useState<CreateCampaignRequest>({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    discount_type: 'percentage',
    discount_percent: 0,
    discount_amount: 0,
    is_cashback: true, // Default açık
  });

  useEffect(() => {
    loadCampaigns();
  }, [filter]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      let data: Campaign[] | PaginatedResponse<Campaign>;

      if (filter === 'active') {
        data = await campaignService.getActive();
        setCampaigns(data);
      } else {
        data = await campaignService.getAll();
        setCampaigns(data);
      }
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setFilter('all');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreate = async () => {
    try {
      await campaignService.create(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: '', description: '', start_date: '', end_date: '', discount_type: 'percentage', discount_percent: 0, discount_amount: 0, is_cashback: true });
      loadCampaigns();
    } catch (error) {
      console.error('Error creating campaign:', error);
    }
  };

  const handleEdit = async () => {
    if (!editingCampaign) return;

    try {
      const updateData: UpdateCampaignRequest = {
        name: formData.name,
        description: formData.description,
        start_date: formData.start_date,
        end_date: formData.end_date,
        discount_type: formData.discount_type,
        discount_percent: formData.discount_percent,
        discount_amount: formData.discount_amount,
        is_cashback: formData.is_cashback,
      };

      await campaignService.update(editingCampaign.id, updateData);
      setIsEditModalOpen(false);
      setEditingCampaign(null);
      loadCampaigns();
    } catch (error) {
      console.error('Error updating campaign:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu kampanyayı silmek istediğinizden emin misiniz?')) return;
    
    try {
      await campaignService.delete(id);
      loadCampaigns();
    } catch (error) {
      console.error('Error deleting campaign:', error);
    }
  };

  const openEditModal = (campaign: Campaign) => {
    setEditingCampaign(campaign);
    setFormData({
      name: campaign.name,
      description: campaign.description,
      start_date: campaign.start_date,
      end_date: campaign.end_date,
      discount_type: campaign.discount_type,
      discount_percent: campaign.discount_percent,
      discount_amount: campaign.discount_amount,
      is_cashback: campaign.is_cashback,
    });
    setIsEditModalOpen(true);
  };

  const isActive = (campaign: Campaign) => {
    const now = new Date();
    const startDate = new Date(campaign.start_date);
    const endDate = new Date(campaign.end_date);
    return now >= startDate && now <= endDate;
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter campaigns based on search term
  const filteredCampaigns = campaigns.filter(campaign => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return (
      normalizeTurkish(campaign.name).includes(normalizedSearchTerm) ||
      normalizeTurkish(campaign.description).includes(normalizedSearchTerm)
    );
  });

  const columns: Column<Campaign>[] = [
    {
      key: 'name',
      header: 'Kampanya',
      render: (campaign) => (
        <div className="flex items-center">
          <Tag className="h-5 w-5 text-purple-600 mr-2" />
          <div>
            <div className="font-medium text-gray-900">{campaign.name}</div>
            <div className="text-sm text-gray-500">{campaign.description}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'discount',
      header: 'İndirim',
      render: (campaign) => (
        <div className="flex items-center">
          {campaign.discount_type === 'percentage' ? (
            <>
              <span className="font-medium">{campaign.discount_percent}%</span>
            </>
          ) : (
            <>
              <span className="font-medium">{formatPrice(campaign.discount_amount)}</span>
            </>
          )}
        </div>
      ),
    },
    {
      key: 'dates',
      header: 'Tarih Aralığı',
      render: (campaign) => (
        <div className="text-sm">
          <div>{new Date(campaign.start_date).toLocaleDateString('tr-TR')}</div>
          <div className="text-gray-500">-</div>
          <div>{new Date(campaign.end_date).toLocaleDateString('tr-TR')}</div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Durum',
      render: (campaign) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          isActive(campaign)
            ? 'bg-green-100 text-green-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {isActive(campaign) ? 'Aktif' : 'Pasif'}
        </span>
      ),
    },
    {
      key: 'cashback',
      header: 'Geri Ödeme',
      render: (campaign) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          campaign.is_cashback
            ? 'bg-blue-100 text-blue-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {campaign.is_cashback ? 'Evet' : 'Hayır'}
        </span>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      className: 'text-right',
      render: (campaign) => (
        <div className="flex justify-end space-x-2">
          <Button
            size="sm"
            variant="success"
            onClick={() => router.push(`/campaigns/${campaign.id}`)}
            title="Detay"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="info"
            onClick={() => openEditModal(campaign)}
            title="Düzenle"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(campaign.id)}
            title="Sil"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Kampanyalar</h1>
            <p className="text-gray-600">Pazarlama kampanyalarınızı yönetin</p>
          </div>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Kampanya
          </Button>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Tümü
          </button>
          <button
            onClick={() => setFilter('active')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === 'active'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Aktif
          </button>
        </div>

        {/* Campaigns Display */}
        {filter === 'active' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {campaigns.map((campaign) => (
              <div key={campaign.id} className="bg-white rounded-lg border p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Tag className="h-5 w-5 text-purple-600 mr-2" />
                    <h3 className="font-medium text-gray-900">{campaign.name}</h3>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    isActive(campaign)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {isActive(campaign) ? 'Aktif' : 'Pasif'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{campaign.description}</p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">İndirim:</span>
                    <span className="font-medium">
                      {campaign.discount_type === 'percentage'
                        ? `${campaign.discount_percent}%`
                        : formatPrice(campaign.discount_amount)
                      }
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Tarih:</span>
                    <span className="font-medium">
                      {new Date(campaign.start_date).toLocaleDateString('tr-TR')} - {new Date(campaign.end_date).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => openEditModal(campaign)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="danger"
                    onClick={() => handleDelete(campaign.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            {/* Search and Filters */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Kampanya adı veya açıklama ara..."
                    value={searchInput}
                    onChange={(e) => handleSearchInputChange(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Button
                  onClick={handleSearch}
                  variant="primary"
                  className="px-4 py-2"
                >
                  Ara
                </Button>
                {(searchTerm || filter !== 'all') && (
                  <Button
                    onClick={handleClearSearch}
                    variant="secondary"
                    className="px-4 py-2"
                  >
                    Temizle
                  </Button>
                )}
              </div>

              {/* Search Summary */}
              {searchTerm && (
                <div className="mt-4 text-sm text-gray-600">
                  {filteredCampaigns.length} kampanya gösteriliyor • &quot;{searchTerm}&quot; araması
                </div>
              )}
            </div>

            <DataTable
              columns={columns}
              data={filteredCampaigns}
              pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredCampaigns.length, total_pages: Math.ceil(filteredCampaigns.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredCampaigns.length / pagination.per_page), has_prev: pagination.page > 1 }}
              onPageChange={handlePageChange}
              onPerPageChange={handlePerPageChange}
              loading={loading}
              emptyMessage="Henüz kampanya kaydı bulunmuyor"
              emptyIcon={<Tag className="h-12 w-12 text-gray-400" />}
              useClientPagination={true}
            />
          </>
        )}


        {/* Create Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Yeni Kampanya Oluştur"
        >
          <div className="space-y-4">
            <Input
              label="Kampanya Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Kampanya adını girin"
            />
            <Input
              label="Açıklama"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Kampanya açıklaması"
            />
            <Input
              label="Başlangıç Tarihi"
              type="date"
              value={formData.start_date}
              onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
            />
            <Input
              label="Bitiş Tarihi"
              type="date"
              value={formData.end_date}
              onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                İndirim Tipi
              </label>
              <select
                value={formData.discount_type}
                onChange={(e) => setFormData({ ...formData, discount_type: e.target.value as 'percentage' | 'amount' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="percentage">Yüzdelik İndirim (%)</option>
                <option value="amount">Miktar İndirim (₺)</option>
              </select>
            </div>

            {formData.discount_type === 'percentage' ? (
              <Input
                label="İndirim Oranı (%)"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.discount_percent}
                onChange={(e) => setFormData({ ...formData, discount_percent: parseFloat(e.target.value) || 0 })}
                placeholder="İndirim oranını girin (örn: 20)"
              />
            ) : (
              <Input
                label="İndirim Miktarı (₺)"
                type="number"
                min="0"
                step="0.01"
                value={formData.discount_amount}
                onChange={(e) => setFormData({ ...formData, discount_amount: parseFloat(e.target.value) || 0 })}
                placeholder="İndirim miktarını girin (örn: 500)"
              />
            )}

            {/* Cashback Switch */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cashback Kampanyası
                </label>
                <p className="text-xs text-gray-500">
                  Açık olduğunda kampanya indirimi üst kuruluş tarafından geri ödenir
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_cashback}
                  onChange={(e) => setFormData({ ...formData, is_cashback: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleCreate}>
                Kampanya Oluştur
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Kampanya Düzenle"
        >
          <div className="space-y-4">
            <Input
              label="Kampanya Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Kampanya adını girin"
            />
            <Input
              label="Açıklama"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Kampanya açıklaması"
            />
            <Input
              label="Başlangıç Tarihi"
              type="date"
              value={formData.start_date}
              onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
            />
            <Input
              label="Bitiş Tarihi"
              type="date"
              value={formData.end_date}
              onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                İndirim Tipi
              </label>
              <select
                value={formData.discount_type}
                onChange={(e) => setFormData({ ...formData, discount_type: e.target.value as 'percentage' | 'amount' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="percentage">Yüzdelik İndirim (%)</option>
                <option value="amount">Miktar İndirim (₺)</option>
              </select>
            </div>

            {formData.discount_type === 'percentage' ? (
              <Input
                label="İndirim Oranı (%)"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.discount_percent}
                onChange={(e) => setFormData({ ...formData, discount_percent: parseFloat(e.target.value) || 0 })}
                placeholder="İndirim oranını girin (örn: 20)"
              />
            ) : (
              <Input
                label="İndirim Miktarı (₺)"
                type="number"
                min="0"
                step="0.01"
                value={formData.discount_amount}
                onChange={(e) => setFormData({ ...formData, discount_amount: parseFloat(e.target.value) || 0 })}
                placeholder="İndirim miktarını girin (örn: 500)"
              />
            )}

            {/* Cashback Switch */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cashback Kampanyası
                </label>
                <p className="text-xs text-gray-500">
                  Açık olduğunda kampanya indirimi üst kuruluş tarafından geri ödenir
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_cashback}
                  onChange={(e) => setFormData({ ...formData, is_cashback: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleEdit}>
                Güncelle
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}
