'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DetailPage, { DetailSection, DetailField, DetailGrid } from '@/components/ui/DetailPage';
import Button from '@/components/ui/Button';
import { Customer } from '@/types';
import { customerService } from '@/services/customerService';
import { formatDate, formatTC, formatPhone } from '@/lib/utils';
import { Edit, Trash2, Users, Phone, MapPin, CreditCard, Calendar } from 'lucide-react';

export default function CustomerDetailPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCustomer();
  }, [id]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await customerService.getById(id);
      if (!data) {
        setError('Müşteri bulunamadı.');
        return;
      }
      setCustomer(data);
    } catch (error) {
      console.error('Error loading customer:', error);
      setError('Müşteri bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };



  const actions = customer && (
    <>
      <Button
        variant="info"
        onClick={() => {/* TODO: Edit modal */}}
        className="flex items-center"
      >
        <Edit className="h-4 w-4 mr-2" />
        Düzenle
      </Button>
      <Button
        variant="danger"
        onClick={() => {/* TODO: Delete confirmation */}}
        className="flex items-center"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Sil
      </Button>
    </>
  );

  return (
    <DetailPage
      title={customer?.name || 'Müşteri Detayı'}
      subtitle="Müşteri bilgilerini görüntüleyin"
      loading={loading}
      error={error}
      backUrl="/customers"
      actions={actions}
    >
      {customer && (
        <DetailGrid columns={2}>
          {/* Kişisel Bilgiler */}
          <DetailSection title="Kişisel Bilgiler">
            <div className="space-y-3">
              <DetailField 
                label="Ad Soyad" 
                value={
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-medium">{customer.name}</span>
                  </div>
                } 
              />
              <DetailField 
                label="TC Kimlik No" 
                value={
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 text-green-600 mr-2" />
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      {formatTC(customer.tc)}
                    </span>
                  </div>
                } 
              />
              <DetailField 
                label="Telefon" 
                value={
                  customer.phone ? (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-purple-600 mr-2" />
                      <a 
                        href={`tel:${customer.phone}`}
                        className="text-purple-600 hover:text-purple-800 font-medium"
                      >
                        {formatPhone(customer.phone)}
                      </a>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
            </div>
          </DetailSection>

          {/* İletişim Bilgileri */}
          <DetailSection title="İletişim Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Adres" 
                value={
                  customer.address ? (
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{customer.address}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Adres belirtilmemiş</span>
                  )
                } 
              />
            </div>
          </DetailSection>

          {/* Tarih Bilgileri */}
          <DetailSection title="Kayıt Bilgileri" className="md:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DetailField 
                label="Kayıt Tarihi" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-green-600 mr-2" />
                    <span>{formatDate(customer.created_at)}</span>
                  </div>
                } 
              />
              <DetailField 
                label="Son Güncelleme" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                    <span>{formatDate(customer.updated_at)}</span>
                  </div>
                } 
              />
            </div>
          </DetailSection>

          {/* Müşteri Özeti */}
          <DetailSection title="Müşteri Özeti" className="md:col-span-2">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-blue-900">Müşteri Bilgi Kartı</h3>
              </div>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Ad Soyad:</strong> {customer.name}</p>
                <p><strong>TC:</strong> {formatTC(customer.tc)}</p>
                {customer.phone && <p><strong>Telefon:</strong> {formatPhone(customer.phone)}</p>}
                {customer.address && <p><strong>Adres:</strong> {customer.address}</p>}
                <p><strong>Kayıt:</strong> {formatDate(customer.created_at)}</p>
              </div>
            </div>
          </DetailSection>
        </DetailGrid>
      )}
    </DetailPage>
  );
}
