'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Modal from '@/components/ui/Modal';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Users, Plus, Edit, Trash2, Search, Eye } from 'lucide-react';
import { Customer, CreateCustomerRequest, UpdateCustomerRequest, PaginationRequest } from '@/types';
import { customerService } from '@/services/customerService';

export default function CustomersPage() {
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  const [formData, setFormData] = useState<CreateCustomerRequest>({
    name: '',
    phone: '',
    tc: '',
    address: '',
  });

  useEffect(() => {
    loadCustomers();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const data = await customerService.getAll();
      setCustomers(data);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreate = async () => {
    try {
      await customerService.create(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: '', phone: '', tc: '', address: '' });
      loadCustomers();
    } catch (error) {
      console.error('Error creating customer:', error);
      alert('Müşteri oluşturulurken hata oluştu: ' + (error as Error).message);
    }
  };

  const openEditModal = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone: customer.phone,
      tc: customer.tc,
      address: customer.address,
    });
    setIsEditModalOpen(true);
  };

  const handleEdit = async () => {
    if (!editingCustomer) return;
    
    try {
      const updateData: UpdateCustomerRequest = {
        name: formData.name,
        phone: formData.phone,
        tc: formData.tc,
      };
      
      await customerService.update(editingCustomer.id, updateData);
      setIsEditModalOpen(false);
      setEditingCustomer(null);
      loadCustomers();
    } catch (error) {
      console.error('Error updating customer:', error);
      alert('Müşteri güncellenirken hata oluştu: ' + (error as Error).message);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu müşteriyi silmek istediğinizden emin misiniz?')) return;
    
    try {
      await customerService.delete(id);
      loadCustomers();
    } catch (error) {
      console.error('Error deleting customer:', error);
      alert('Müşteri silinirken hata oluştu: ' + (error as Error).message);
    }
  };

  const formatDate = (dateString: string) => {
    const [datePart, timePart] = dateString.split(' ');
    const [year, month, day] = datePart.split('-');
    const [hour, minute] = timePart.split(':');
    return `${day}.${month}.${year} ${hour}:${minute}`;
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return (
      normalizeTurkish(customer.name).includes(normalizedSearchTerm) ||
      normalizeTurkish(customer.phone).includes(normalizedSearchTerm) ||
      normalizeTurkish(customer.tc).includes(normalizedSearchTerm) ||
      (customer.address && normalizeTurkish(customer.address).includes(normalizedSearchTerm))
    );
  });

  const columns: Column<Customer>[] = [
    {
      key: 'name',
      header: 'Müşteri',
      render: (customer) => (
        <div className="flex items-center">
          <Users className="h-5 w-5 text-blue-600 mr-2" />
          <div className="font-medium text-gray-900">{customer.name}</div>
        </div>
      ),
    },
    {
      key: 'tc',
      header: 'TC Kimlik',
      render: (customer) => customer.tc,
    },
    {
      key: 'phone',
      header: 'Telefon',
      render: (customer) => customer.phone || '-',
    },
    {
      key: 'address',
      header: 'Adres',
      render: (customer) => customer.address || '-',
    },
    {
      key: 'created_at',
      header: 'Kayıt Tarihi',
      render: (customer) => formatDate(customer.created_at),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      className: 'text-right',
      render: (customer) => (
        <div className="flex justify-end space-x-2">
          <Button
            size="sm"
            variant="success"
            onClick={() => router.push(`/customers/${customer.id}`)}
            title="Detay"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="info"
            onClick={() => openEditModal(customer)}
            title="Düzenle"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(customer.id)}
            title="Sil"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Müşteriler</h1>
            <p className="text-gray-600">Kayıtlı müşterileri görüntüleyin ve yönetin</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Müşteri
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Müşteri adı, telefon, TC veya adres ara..."
                value={searchInput}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="primary"
              className="px-4 py-2"
            >
              Ara
            </Button>
            {(searchTerm) && (
              <Button
                onClick={handleClearSearch}
                variant="secondary"
                className="px-4 py-2"
              >
                Temizle
              </Button>
            )}
          </div>

          {/* Search Summary */}
          {searchTerm && (
            <div className="mt-4 text-sm text-gray-600">
              {filteredCustomers.length} müşteri gösteriliyor • &quot;{searchTerm}&quot; araması
            </div>
          )}
        </div>

        {/* Customers Display */}
        <DataTable
          columns={columns}
          data={filteredCustomers}
          pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredCustomers.length, total_pages: Math.ceil(filteredCustomers.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredCustomers.length / pagination.per_page), has_prev: pagination.page > 1 }}
          onPageChange={handlePageChange}
          onPerPageChange={handlePerPageChange}
          loading={loading}
          emptyMessage="Henüz müşteri kaydı bulunmuyor"
          emptyIcon={<Users className="h-12 w-12 text-gray-400" />}
          useClientPagination={true}
        />
      </div>

      {/* Create Customer Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setFormData({ name: '', phone: '', tc: '', address: '' });
        }}
        title="Yeni Müşteri Oluştur"
      >
        <div className="space-y-4">
          <Input
            label="Müşteri Adı"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />

          <Input
            label="Telefon"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
          />

          <Input
            label="TC Kimlik No"
            value={formData.tc}
            onChange={(e) => setFormData(prev => ({ ...prev, tc: e.target.value }))}
            required
          />

          <Input
            label="Adres"
            value={formData.address}
            onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
            placeholder="Müşteri adresini girin..."
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                setIsCreateModalOpen(false);
                setFormData({ name: '', phone: '', tc: '', address: '' });
              }}
            >
              İptal
            </Button>
            <Button onClick={handleCreate}>
              Oluştur
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Customer Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingCustomer(null);
        }}
        title="Müşteri Düzenle"
      >
        <div className="space-y-4">
          <Input
            label="Müşteri Adı"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />

          <Input
            label="Telefon"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
          />

          <Input
            label="TC Kimlik No"
            value={formData.tc}
            onChange={(e) => setFormData(prev => ({ ...prev, tc: e.target.value }))}
            required
          />

          <Input
            label="Adres"
            value={formData.address}
            onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
            placeholder="Müşteri adresini girin..."
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                setIsEditModalOpen(false);
                setEditingCustomer(null);
              }}
            >
              İptal
            </Button>
            <Button onClick={handleEdit}>
              Güncelle
            </Button>
          </div>
        </div>
      </Modal>
    </MainLayout>
  );
}
