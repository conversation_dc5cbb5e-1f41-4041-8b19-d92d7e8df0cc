'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Modal from '@/components/ui/Modal';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Building2, Plus, Edit, Trash2, ArrowLeft, Users } from 'lucide-react';
import { Organization, CreateOrganizationRequest } from '@/types';
import { organizationService } from '@/services/organizationService';

export default function OrganizationDetailPage() {
  const router = useRouter();
  const params = useParams();
  const organizationId = params.id as string;

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [subOrganizations, setSubOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateSubOrgModalOpen, setIsCreateSubOrgModalOpen] = useState(false);
  const [createSubOrgFormData, setCreateSubOrgFormData] = useState<CreateOrganizationRequest>({
    name: '',
    description: '',
  });

  useEffect(() => {
    if (organizationId) {
      loadOrganizationDetails();
      loadSubOrganizations();
    }
  }, [organizationId]);

  const loadOrganizationDetails = async () => {
    try {
      setLoading(true);
      const data = await organizationService.getOrganizationById(organizationId);
      setOrganization(data);
    } catch (error) {
      console.error('Error loading organization details:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSubOrganizations = async () => {
    try {
      const data = await organizationService.getSubOrganizations(organizationId);
      setSubOrganizations(data);
    } catch (error) {
      console.error('Error loading sub-organizations:', error);
    }
  };

  const handleCreateSubOrganization = async () => {
    try {
      await organizationService.createSubOrganization(organizationId, {
        ...createSubOrgFormData,
        is_main: false,
        main_org_id: organizationId,
      });
      setIsCreateSubOrgModalOpen(false);
      setCreateSubOrgFormData({ name: '', description: '' });
      loadSubOrganizations();
    } catch (error) {
      console.error('Error creating sub-organization:', error);
    }
  };

  const handleDeleteSubOrganization = async (subOrgId: string) => {
    if (window.confirm('Bu alt organizasyonu silmek istediğinizden emin misiniz?')) {
      try {
        await organizationService.deleteOrganization(subOrgId);
        loadSubOrganizations();
      } catch (error) {
        console.error('Error deleting sub-organization:', error);
      }
    }
  };

  const subOrgColumns: Column<Organization>[] = [
    {
      key: 'name',
      header: 'Organizasyon Adı',
      render: (subOrg) => (
        <div className="font-medium text-gray-900">{subOrg.name}</div>
      ),
    },
    {
      key: 'description',
      header: 'Açıklama',
      render: (subOrg) => (
        <div className="text-gray-600">{subOrg.description || '-'}</div>
      ),
    },
    {
      key: 'is_active',
      header: 'Durum',
      render: (subOrg) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          subOrg.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {subOrg.is_active ? 'Aktif' : 'Pasif'}
        </span>
      ),
    },
    {
      key: 'created_at',
      header: 'Oluşturulma Tarihi',
      render: (subOrg) => (
        <div className="text-gray-600">{subOrg.created_at}</div>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      render: (subOrg) => (
        <div className="flex items-center space-x-2 justify-end">
          <Button
            variant="danger"
            size="sm"
            onClick={() => handleDeleteSubOrganization(subOrg.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  if (!organization) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-red-600">Organizasyon bulunamadı</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="secondary"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Geri
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{organization.name}</h1>
                <p className="text-gray-600">Organizasyon Detayları</p>
              </div>
            </div>
          </div>

          {/* Organization Details */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Organizasyon Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Organizasyon Adı
                </label>
                <div className="text-gray-900">{organization.name}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Açıklama
                </label>
                <div className="text-gray-900">{organization.description || '-'}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Durum
                </label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  organization.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {organization.is_active ? 'Aktif' : 'Pasif'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tip
                </label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  organization.is_main 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-purple-100 text-purple-800'
                }`}>
                  {organization.is_main ? 'Ana Organizasyon' : 'Alt Organizasyon'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Oluşturulma Tarihi
                </label>
                <div className="text-gray-900">{organization.created_at}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Güncellenme Tarihi
                </label>
                <div className="text-gray-900">{organization.updated_at}</div>
              </div>
            </div>
          </div>

          {/* Sub Organizations Section - Only show for main organizations */}
          {organization.is_main && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Alt Organizasyonlar</h2>
                    <p className="text-gray-600">Bu organizasyona bağlı alt organizasyonlar</p>
                  </div>
                  <Button onClick={() => setIsCreateSubOrgModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Alt Organizasyon Ekle
                  </Button>
                </div>
              </div>
              
              <div className="p-6">
                {subOrganizations.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {subOrgColumns.map((column) => (
                            <th
                              key={column.key}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              {column.header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {subOrganizations.map((subOrg) => (
                          <tr key={subOrg.id}>
                            {subOrgColumns.map((column) => (
                              <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                                {column.render(subOrg)}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Henüz alt organizasyon bulunmuyor</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Create Sub Organization Modal */}
          <Modal
            isOpen={isCreateSubOrgModalOpen}
            onClose={() => setIsCreateSubOrgModalOpen(false)}
            title="Alt Organizasyon Ekle"
          >
            <div className="space-y-4">
              <Input
                label="Organizasyon Adı"
                value={createSubOrgFormData.name}
                onChange={(e) => setCreateSubOrgFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Alt organizasyon adını girin"
                required
              />
              <Input
                label="Açıklama"
                value={createSubOrgFormData.description}
                onChange={(e) => setCreateSubOrgFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Açıklama girin (opsiyonel)"
              />
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="secondary"
                  onClick={() => setIsCreateSubOrgModalOpen(false)}
                >
                  İptal
                </Button>
                <Button
                  onClick={handleCreateSubOrganization}
                  disabled={!createSubOrgFormData.name.trim()}
                >
                  Alt Organizasyon Ekle
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
