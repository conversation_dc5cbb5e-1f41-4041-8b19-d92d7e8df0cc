'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Modal from '@/components/ui/Modal';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, Search, Eye } from 'lucide-react';
import { Organization, CreateOrganizationRequest, UpdateOrganizationRequest, PaginationRequest } from '@/types';
import { organizationService } from '@/services/organizationService';

export default function OrganizationsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);

  // Form states
  const [formData, setFormData] = useState<CreateOrganizationRequest>({
    name: '',
    description: '',
  });

  const [updateFormData, setUpdateFormData] = useState<UpdateOrganizationRequest>({
    name: '',
    description: '',
    is_active: true,
  });

  useEffect(() => {
    loadOrganizations();
  }, []);

  const loadOrganizations = async () => {
    try {
      setLoading(true);
      const data = await organizationService.getMainOrganizations();
      setOrganizations(data);
    } catch (error) {
      console.error('Error loading organizations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreate = async () => {
    try {
      await organizationService.createOrganization(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: '', description: '' });
      loadOrganizations();
    } catch (error) {
      console.error('Error creating organization:', error);
      alert('Organizasyon oluşturulurken hata oluştu: ' + (error as Error).message);
    }
  };

  const openEditModal = (organization: Organization) => {
    setEditingOrganization(organization);
    setUpdateFormData({
      name: organization.name,
      description: organization.description,
      is_active: organization.is_active,
    });
    setIsEditModalOpen(true);
  };

  const handleUpdate = async () => {
    if (!editingOrganization) return;

    try {
      await organizationService.updateOrganization(editingOrganization.id, updateFormData);
      setIsEditModalOpen(false);
      setEditingOrganization(null);
      setUpdateFormData({ name: '', description: '', is_active: true });
      loadOrganizations();
    } catch (error) {
      console.error('Error updating organization:', error);
      alert('Organizasyon güncellenirken hata oluştu: ' + (error as Error).message);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu organizasyonu silmek istediğinizden emin misiniz?')) return;

    try {
      await organizationService.deleteOrganization(id);
      loadOrganizations();
    } catch (error) {
      console.error('Error deleting organization:', error);
      alert('Organizasyon silinirken hata oluştu: ' + (error as Error).message);
    }
  };

  const handleViewDetails = (id: string) => {
    router.push(`/admin/organizations/${id}`);
  };

  // Filter organizations based on search term
  const filteredOrganizations = organizations.filter(org =>
    org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    org.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const columns: Column<Organization>[] = [
    {
      key: 'name',
      header: 'Organizasyon Adı',
      render: (organization) => (
        <div className="font-medium text-gray-900">{organization.name}</div>
      ),
    },
    {
      key: 'description',
      header: 'Açıklama',
      render: (organization) => (
        <div className="text-gray-600">{organization.description}</div>
      ),
    },
    {
      key: 'is_active',
      header: 'Durum',
      render: (organization) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          organization.is_active
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {organization.is_active ? 'Aktif' : 'Pasif'}
        </span>
      ),
    },
    {
      key: 'created_at',
      header: 'Oluşturulma Tarihi',
      render: (organization) => (
        <div className="text-gray-600">{organization.created_at}</div>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      render: (organization) => (
        <div className="flex items-center space-x-2 justify-end">
          <Button
            variant="danger"
            size="sm"
            onClick={() => handleDelete(organization.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="info"
            size="sm"
            onClick={() => openEditModal(organization)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="success"
            size="sm"
            onClick={() => handleViewDetails(organization.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Organizasyon Yönetimi</h1>
              <p className="text-gray-600">Sistem organizasyonlarını yönetin</p>
            </div>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Organizasyon
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="flex gap-2">
                  <Input
                    placeholder="Organizasyon adı veya açıklama ile ara..."
                    value={searchInput}
                    onChange={(e) => handleSearchInputChange(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleSearch} variant="secondary">
                    <Search className="h-4 w-4" />
                  </Button>
                  {searchTerm && (
                    <Button onClick={handleClearSearch} variant="secondary">
                      Temizle
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Organizations Table */}
          <div className="bg-white rounded-lg shadow-sm border">
            <DataTable
              data={filteredOrganizations}
              columns={columns}
              pagination={{
                page: pagination.page,
                per_page: pagination.per_page,
                total: filteredOrganizations.length,
                total_pages: Math.ceil(filteredOrganizations.length / pagination.per_page),
                has_next: pagination.page < Math.ceil(filteredOrganizations.length / pagination.per_page),
                has_prev: pagination.page > 1,
              }}
              onPageChange={handlePageChange}
              onPerPageChange={handlePerPageChange}
              useClientPagination={true}
            />
          </div>

          {/* Create Organization Modal */}
          <Modal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            title="Yeni Organizasyon Oluştur"
          >
            <div className="space-y-4">
              <Input
                label="Organizasyon Adı"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Organizasyon adını girin"
                required
              />
              <Input
                label="Açıklama"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Organizasyon açıklamasını girin"
              />
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="secondary"
                  onClick={() => setIsCreateModalOpen(false)}
                >
                  İptal
                </Button>
                <Button onClick={handleCreate}>
                  Oluştur
                </Button>
              </div>
            </div>
          </Modal>

          {/* Edit Organization Modal */}
          <Modal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            title="Organizasyon Düzenle"
          >
            <div className="space-y-4">
              <Input
                label="Organizasyon Adı"
                value={updateFormData.name}
                onChange={(e) => setUpdateFormData({ ...updateFormData, name: e.target.value })}
                placeholder="Organizasyon adını girin"
              />
              <Input
                label="Açıklama"
                value={updateFormData.description}
                onChange={(e) => setUpdateFormData({ ...updateFormData, description: e.target.value })}
                placeholder="Organizasyon açıklamasını girin"
              />
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={updateFormData.is_active}
                  onChange={(e) => setUpdateFormData({ ...updateFormData, is_active: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                  Aktif
                </label>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="secondary"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  İptal
                </Button>
                <Button onClick={handleUpdate}>
                  Güncelle
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
