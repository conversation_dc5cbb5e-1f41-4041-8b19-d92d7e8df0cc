'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Label } from '../../../components/ui/Label';
import { Switch } from '../../../components/ui/Switch';
import { Separator } from '../../../components/ui/Separator';
import { Save, Settings, Database, Bell, Shield, Palette } from 'lucide-react';

interface SystemSettings {
  company_name: string;
  company_address: string;
  company_phone: string;
  company_email: string;
  tax_number: string;
  currency_symbol: string;
  default_tax_rate: number;
  low_stock_threshold: number;
  enable_notifications: boolean;
  enable_auto_backup: boolean;
  backup_frequency: string;
  theme: string;
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>({
    company_name: '',
    company_address: '',
    company_phone: '',
    company_email: '',
    tax_number: '',
    currency_symbol: 'TL',
    default_tax_rate: 18,
    low_stock_threshold: 10,
    enable_notifications: true,
    enable_auto_backup: false,
    backup_frequency: 'daily',
    theme: 'light'
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Ayarlar yüklenirken hata:', error);
      alert('Ayarlar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        alert('Ayarlar başarıyla kaydedildi');
      } else {
        throw new Error('Kaydetme işlemi başarısız');
      }
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata:', error);
      alert('Ayarlar kaydedilirken hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-3">
          <Settings className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Sistem Ayarları</h1>
            <p className="text-gray-600">Sistem genelindeki ayarları yönetin</p>
          </div>
        </div>
        <Button 
          onClick={handleSave} 
          disabled={saving}
          className="flex items-center space-x-2"
        >
          <Save className="h-4 w-4" />
          <span>{saving ? 'Kaydediliyor...' : 'Kaydet'}</span>
        </Button>
      </div>

      <div className="grid gap-6">
        {/* Şirket Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Şirket Bilgileri</span>
            </CardTitle>
            <CardDescription>
              Şirketinizin temel bilgilerini düzenleyin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company_name">Şirket Adı</Label>
                <Input
                  id="company_name"
                  value={settings.company_name}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('company_name', e.target.value)}
                  placeholder="Şirket adını girin"
                />
              </div>
              <div>
                <Label htmlFor="tax_number">Vergi Numarası</Label>
                <Input
                  id="tax_number"
                  value={settings.tax_number}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('tax_number', e.target.value)}
                  placeholder="Vergi numarasını girin"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="company_address">Şirket Adresi</Label>
              <Input
                id="company_address"
                value={settings.company_address}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('company_address', e.target.value)}
                placeholder="Şirket adresini girin"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company_phone">Telefon</Label>
                <Input
                  id="company_phone"
                  value={settings.company_phone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('company_phone', e.target.value)}
                  placeholder="Telefon numarasını girin"
                />
              </div>
              <div>
                <Label htmlFor="company_email">E-posta</Label>
                <Input
                  id="company_email"
                  type="email"
                  value={settings.company_email}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('company_email', e.target.value)}
                  placeholder="E-posta adresini girin"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Finansal Ayarlar */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Finansal Ayarlar</span>
            </CardTitle>
            <CardDescription>
              Para birimi ve vergi ayarlarını düzenleyin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="currency_symbol">Para Birimi</Label>
                <Input
                  id="currency_symbol"
                  value={settings.currency_symbol}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('currency_symbol', e.target.value)}
                  placeholder="TL"
                />
              </div>
              <div>
                <Label htmlFor="default_tax_rate">Varsayılan KDV Oranı (%)</Label>
                <Input
                  id="default_tax_rate"
                  type="number"
                  value={settings.default_tax_rate}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('default_tax_rate', Number(e.target.value))}
                  placeholder="18"
                />
              </div>
              <div>
                <Label htmlFor="low_stock_threshold">Düşük Stok Uyarı Limiti</Label>
                <Input
                  id="low_stock_threshold"
                  type="number"
                  value={settings.low_stock_threshold}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('low_stock_threshold', Number(e.target.value))}
                  placeholder="10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sistem Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Sistem Ayarları</span>
            </CardTitle>
            <CardDescription>
              Bildirimler ve yedekleme ayarlarını düzenleyin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Bildirimleri Etkinleştir</Label>
                <p className="text-sm text-gray-500">
                  Sistem bildirimleri ve uyarıları alın
                </p>
              </div>
              <Switch
                checked={settings.enable_notifications}
                onCheckedChange={(checked: boolean) => handleInputChange('enable_notifications', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Otomatik Yedekleme</Label>
                <p className="text-sm text-gray-500">
                  Veritabanının otomatik yedeklenmesini etkinleştir
                </p>
              </div>
              <Switch
                checked={settings.enable_auto_backup}
                onCheckedChange={(checked: boolean) => handleInputChange('enable_auto_backup', checked)}
              />
            </div>
            
            {settings.enable_auto_backup && (
              <div>
                <Label htmlFor="backup_frequency">Yedekleme Sıklığı</Label>
                <select
                  id="backup_frequency"
                  value={settings.backup_frequency}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('backup_frequency', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="daily">Günlük</option>
                  <option value="weekly">Haftalık</option>
                  <option value="monthly">Aylık</option>
                </select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Görünüm Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Palette className="h-5 w-5" />
              <span>Görünüm Ayarları</span>
            </CardTitle>
            <CardDescription>
              Arayüz teması ve görünüm ayarlarını düzenleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="theme">Tema</Label>
              <select
                id="theme"
                value={settings.theme}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('theme', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="light">Açık Tema</option>
                <option value="dark">Koyu Tema</option>
                <option value="auto">Sistem Ayarı</option>
              </select>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
