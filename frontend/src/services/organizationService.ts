import { apiClient } from '@/lib/api';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const organizationService = {
  // Organization Management (Admin only)
  async createOrganization(organizationData: CreateOrganizationRequest): Promise<void> {
    await apiClient.post<{data: void}>('/organizations', organizationData);
  },

  async getAllOrganizations(): Promise<Organization[]> {
    try {
      const response = await apiClient.get<{data: Organization[]}>('/organizations');
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching organizations:', error);
      return [];
    }
  },

  async getOrganizationsPaginated(params: PaginationRequest): Promise<PaginatedResponse<Organization>> {
    try {
      const response = await apiClient.get<{data: PaginatedResponse<Organization>}>('/organizations/paginated', params);
      return response?.data || {
        data: [],
        pagination: {
          page: 1,
          per_page: 10,
          total: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false
        }
      };
    } catch (error) {
      console.error('Error fetching paginated organizations:', error);
      return {
        data: [],
        pagination: {
          page: 1,
          per_page: 10,
          total: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false
        }
      };
    }
  },

  async getOrganizationById(id: string): Promise<Organization> {
    try {
      const response = await apiClient.get<{data: Organization}>(`/organizations/${id}`);
      return response?.data;
    } catch (error) {
      console.error('Error fetching organization by id:', error);
      throw error;
    }
  },

  async updateOrganization(id: string, organizationData: UpdateOrganizationRequest): Promise<void> {
    await apiClient.put<{data: void}>(`/organizations/${id}`, organizationData);
  },

  async deleteOrganization(id: string): Promise<void> {
    await apiClient.delete<{data: void}>(`/organizations/${id}`);
  },

  // Sub-organization Management
  async createSubOrganization(mainOrgId: string, organizationData: CreateOrganizationRequest): Promise<void> {
    await apiClient.post<{data: void}>(`/organizations/${mainOrgId}/sub-organizations`, organizationData);
  },

  async getSubOrganizations(mainOrgId: string): Promise<Organization[]> {
    try {
      const response = await apiClient.get<{data: Organization[]}>(`/organizations/${mainOrgId}/sub-organizations`);
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching sub organizations:', error);
      return [];
    }
  },

  async getMainOrganizations(): Promise<Organization[]> {
    try {
      const response = await apiClient.get<{data: Organization[]}>('/organizations/main');
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching main organizations:', error);
      return [];
    }
  }
};
