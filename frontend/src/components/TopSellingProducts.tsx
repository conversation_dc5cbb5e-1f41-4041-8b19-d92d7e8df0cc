'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Trophy, Package, TrendingUp, ShoppingCart } from 'lucide-react';
import { TopSellingProduct } from '@/types';
import { reportService } from '@/services/reportService';

interface TopSellingProductsProps {
  year: number;
  month: number;
  limit?: number;
}

export default function TopSellingProducts({ year, month, limit = 5 }: TopSellingProductsProps) {
  const [products, setProducts] = useState<TopSellingProduct[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTopSellingProducts();
  }, [year, month, limit, loadTopSellingProducts]);

  const loadTopSellingProducts = async () => {
    try {
      setLoading(true);
      const data = await reportService.getTopSellingProducts({
        year: year || undefined,
        month: month || undefined,
        limit
      });
      setProducts(data);
    } catch (error) {
      console.error('Error loading top selling products:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Trophy className="h-5 w-5 text-yellow-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">En Çok Satılan Ürünler</h3>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">Yükleniyor...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Trophy className="h-5 w-5 text-yellow-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">En Çok Satılan Ürünler</h3>
          </div>
          <div className="text-sm text-gray-500">
            {month ? `${month}/${year}` : year}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {products.length > 0 ? (
          <div className="space-y-4">
            {products.map((product, index) => (
              <div key={product.product_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                    index === 0 ? 'bg-yellow-100 text-yellow-600' :
                    index === 1 ? 'bg-gray-100 text-gray-600' :
                    index === 2 ? 'bg-orange-100 text-orange-600' :
                    'bg-blue-100 text-blue-600'
                  }`}>
                    {index < 3 ? (
                      <Trophy className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.product_name}</p>
                    <p className="text-xs text-gray-500">Kod: {product.product_code}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900 flex items-center">
                        <Package className="h-3 w-3 mr-1" />
                        {product.total_quantity}
                      </p>
                      <p className="text-xs text-gray-500">Adet</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-green-600 flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {product.total_revenue.toFixed(2)} TL
                      </p>
                      <p className="text-xs text-gray-500">Gelir</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-blue-600 flex items-center">
                        <ShoppingCart className="h-3 w-3 mr-1" />
                        {product.sales_count}
                      </p>
                      <p className="text-xs text-gray-500">Satış</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Bu dönemde satış verisi bulunamadı</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
