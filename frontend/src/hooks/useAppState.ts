import { useAuth } from '@/contexts/AuthContext';
import { useMemo } from 'react';

// Frontend state management hook that mirrors backend state package
export interface AppState {
  // User state
  currentUserID: string | null;
  currentUserOrganizationID: string | null;
  
  // Admin state
  currentAdminID: string | null;
  currentAdminToken: string | null;
  currentDepartmentID: string | null;
  isMainOrg: boolean;
  isAuthorized: boolean;
  
  // Network state
  currentUserIP: string | null;
  currentAdminAgent: string | null;
  
  // API state
  apiKey: string | null;
  apiSecret: string | null;
  
  // Internal auth state
  internalAuth: boolean;
  
  // Computed states
  isAdmin: boolean;
  isAuthenticated: boolean;
}

export function useAppState(): AppState {
  const { user, token, isAuthenticated, isAdmin } = useAuth();

  const state = useMemo((): AppState => {
    if (!user || !token) {
      return {
        currentUserID: null,
        currentUserOrganizationID: null,
        currentAdminID: null,
        currentAdminToken: null,
        currentDepartmentID: null,
        isMainOrg: false,
        isAuthorized: false,
        currentUserIP: null,
        currentAdminAgent: null,
        apiKey: null,
        apiSecret: null,
        internalAuth: false,
        isAdmin: false,
        isAuthenticated: false,
      };
    }

    // Parse JWT token to extract state information
    let tokenData: any = {};
    try {
      const payload = token.split('.')[1];
      const decoded = atob(payload);
      tokenData = JSON.parse(decoded);
    } catch (error) {
      console.error('Error parsing token:', error);
    }

    return {
      // User state
      currentUserID: user.id,
      currentUserOrganizationID: user.organization_id,
      
      // Admin state
      currentAdminID: tokenData.admin_id || user.id,
      currentAdminToken: tokenData.admin_token || null,
      currentDepartmentID: tokenData.department_id || null,
      isMainOrg: tokenData.is_main_org ?? true,
      isAuthorized: tokenData.is_authorized ?? true,
      
      // Network state
      currentUserIP: tokenData.user_ip || null,
      currentAdminAgent: tokenData.admin_agent || null,
      
      // API state
      apiKey: tokenData.api_key || null,
      apiSecret: tokenData.api_secret || null,
      
      // Internal auth state
      internalAuth: tokenData.internal_auth ?? false,
      
      // Computed states
      isAdmin,
      isAuthenticated,
    };
  }, [user, token, isAuthenticated, isAdmin]);

  return state;
}

// Helper functions that mirror backend state package functions
export const useCurrentUser = () => {
  const state = useAppState();
  return state.currentUserID;
};

export const useCurrentUserOrganization = () => {
  const state = useAppState();
  return state.currentUserOrganizationID;
};

export const useCurrentAdminUser = () => {
  const state = useAppState();
  return state.currentAdminID;
};

export const useCurrentAdminOrganization = () => {
  const state = useAppState();
  return state.currentUserOrganizationID; // Same as user organization
};

export const useCurrentDepartment = () => {
  const state = useAppState();
  return state.currentDepartmentID;
};

export const useAmIAuthorized = () => {
  const state = useAppState();
  return state.isAuthorized;
};

export const useCurrentIP = () => {
  const state = useAppState();
  return state.currentUserIP;
};

export const useCurrentAdminAgent = () => {
  const state = useAppState();
  return state.currentAdminAgent;
};

export const useGetCurrentAdminToken = () => {
  const state = useAppState();
  return state.currentAdminToken;
};

export const useCurrentApiKey = () => {
  const state = useAppState();
  return state.apiKey;
};

export const useCurrentApiSecret = () => {
  const state = useAppState();
  return state.apiSecret;
};

export const useInternalAuth = () => {
  const state = useAppState();
  return state.internalAuth;
};
