package routes

import (
	"business-mamagement/pkg/domain"
	"business-mamagement/pkg/dtos"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type SettingHandler struct {
	settingService domain.SettingService
	validator      *validator.Validate
}

func NewSettingHandler(settingService domain.SettingService) *SettingHandler {
	return &SettingHandler{
		settingService: settingService,
		validator:      validator.New(),
	}
}

func (h *SettingHandler) RegisterRoutes(router *gin.RouterGroup) {
	settingRoutes := router.Group("/admin/settings")
	{
		settingRoutes.GET("", h.GetSettings)
		settingRoutes.PUT("", h.UpdateSettings)
	}
}

// GetSettings godoc
// @Summary Get system settings
// @Description Get current system settings
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} dtos.SettingResponseDTO
// @Failure 500 {object} map[string]interface{}
// @Router /api/admin/settings [get]
func (h *SettingHandler) GetSettings(c *gin.Context) {
	settings, err := h.settingService.GetSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Ayarlar alınırken hata oluştu",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateSettings godoc
// @Summary Update system settings
// @Description Update system settings
// @Tags Settings
// @Accept json
// @Produce json
// @Param setting body dtos.SettingUpdateDTO true "Setting update data"
// @Success 200 {object} dtos.SettingResponseDTO
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/admin/settings [put]
func (h *SettingHandler) UpdateSettings(c *gin.Context) {
	var dto dtos.SettingUpdateDTO
	if err := c.ShouldBindJSON(&dto); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Geçersiz veri formatı",
			"details": err.Error(),
		})
		return
	}

	if err := h.validator.Struct(&dto); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Veri doğrulama hatası",
			"details": err.Error(),
		})
		return
	}

	updatedSettings, err := h.settingService.UpdateSettings(&dto)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Ayarlar güncellenirken hata oluştu",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, updatedSettings)
}
