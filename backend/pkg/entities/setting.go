package entities

import (
	"time"

	"github.com/google/uuid"
)

type Setting struct {
	ID                  uint      `json:"id" gorm:"primaryKey"`
	CompanyName         string    `json:"company_name" gorm:"column:company_name"`
	CompanyAddress      string    `json:"company_address" gorm:"column:company_address"`
	CompanyPhone        string    `json:"company_phone" gorm:"column:company_phone"`
	CompanyEmail        string    `json:"company_email" gorm:"column:company_email"`
	TaxNumber           string    `json:"tax_number" gorm:"column:tax_number"`
	CurrencySymbol      string    `json:"currency_symbol" gorm:"column:currency_symbol;default:TL"`
	DefaultTaxRate      float64   `json:"default_tax_rate" gorm:"column:default_tax_rate;default:18"`
	LowStockThreshold   int       `json:"low_stock_threshold" gorm:"column:low_stock_threshold;default:10"`
	EnableNotifications bool      `json:"enable_notifications" gorm:"column:enable_notifications;default:true"`
	EnableAutoBackup    bool      `json:"enable_auto_backup" gorm:"column:enable_auto_backup;default:false"`
	BackupFrequency     string    `json:"backup_frequency" gorm:"column:backup_frequency;default:daily"`
	Theme               string    `json:"theme" gorm:"column:theme;default:light"`
	OrganizationID      uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	CreatedAt           time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"column:updated_at"`
}

func (Setting) TableName() string {
	return "settings"
}
