package organization

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error
	GetAllOrganizations(ctx context.Context) ([]entities.Organization, error)
	GetMainOrganizations(ctx context.Context) ([]entities.Organization, error)
	GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]entities.Organization, error)
	GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error)
	GetOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error)
	UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error
	DeleteOrganization(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error {
	var organization entities.Organization
	organization.Mapper(req)
	return r.db.WithContext(ctx).Create(&organization).Error
}

func (r *repository) GetAllOrganizations(ctx context.Context) ([]entities.Organization, error) {
	var organizations []entities.Organization
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").Order("created_at DESC").Find(&organizations).Error
	if err != nil {
		// If table doesn't exist or other database errors, return empty array
		if strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "relation") {
			return []entities.Organization{}, nil
		}
		return organizations, err
	}
	return organizations, nil
}

func (r *repository) GetMainOrganizations(ctx context.Context) ([]entities.Organization, error) {
	var organizations []entities.Organization
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND is_main = ?", true).Order("created_at DESC").Find(&organizations).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return organizations, nil
		}
		return organizations, err
	}
	return organizations, nil
}

func (r *repository) GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]entities.Organization, error) {
	var organizations []entities.Organization
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND is_main = ? AND main_org_id = ?", false, mainOrgID).Order("created_at DESC").Find(&organizations).Error
	if err != nil {
		// If table doesn't exist or other database errors, return empty array
		if strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "relation") {
			return []entities.Organization{}, nil
		}
		return organizations, err
	}
	return organizations, nil
}

func (r *repository) GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error) {
	var organizations []entities.Organization
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Organization{}).Where("deleted_at IS NULL")

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		// If table doesn't exist, return empty response
		if strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "relation") {
			return dtos.PaginatedResponse[dtos.OrganizationResponse]{
				Data:       []dtos.OrganizationResponse{},
				Pagination: dtos.NewPaginationResponse(req.Page, req.PerPage, 0),
			}, nil
		}
		return dtos.PaginatedResponse[dtos.OrganizationResponse]{}, err
	}

	// Apply pagination
	offset := (req.Page - 1) * req.PerPage
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PerPage).Find(&organizations).Error; err != nil {
		// If table doesn't exist, return empty response
		if strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "relation") {
			return dtos.PaginatedResponse[dtos.OrganizationResponse]{
				Data:       []dtos.OrganizationResponse{},
				Pagination: dtos.NewPaginationResponse(req.Page, req.PerPage, 0),
			}, nil
		}
		return dtos.PaginatedResponse[dtos.OrganizationResponse]{}, err
	}

	// Convert to response DTOs
	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	pagination := dtos.NewPaginationResponse(req.Page, req.PerPage, total)

	return dtos.PaginatedResponse[dtos.OrganizationResponse]{
		Data:       responses,
		Pagination: pagination,
	}, nil
}

func (r *repository) GetOrganizationByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	var organization entities.Organization
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&organization).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("organization not found")
		}
		// If table doesn't exist, return not found error
		if strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "relation") {
			return nil, errors.New("organization not found")
		}
		return nil, err
	}
	return &organization, nil
}

func (r *repository) UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error {
	var organization entities.Organization
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("organization not found")
		}
		return err
	}

	organization.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&organization).Error
}

func (r *repository) DeleteOrganization(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).Delete(&entities.Organization{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("organization not found")
	}
	return nil
}
