package domain

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"time"
)

type SettingService interface {
	GetSettings() (*dtos.SettingResponseDTO, error)
	UpdateSettings(dto *dtos.SettingUpdateDTO) (*dtos.SettingResponseDTO, error)
}

type settingService struct {
	settingRepo SettingRepository
}

func NewSettingService(settingRepo SettingRepository) SettingService {
	return &settingService{
		settingRepo: settingRepo,
	}
}

func (s *settingService) GetSettings() (*dtos.SettingResponseDTO, error) {
	setting, err := s.settingRepo.GetSettings()
	if err != nil {
		return nil, err
	}

	return s.toResponseDTO(setting), nil
}

func (s *settingService) UpdateSettings(dto *dtos.SettingUpdateDTO) (*dtos.SettingResponseDTO, error) {
	// Mevcut ayarları getir
	setting, err := s.settingRepo.GetSettings()
	if err != nil {
		return nil, err
	}

	// DTO'dan entity'ye güncelle
	s.updateEntityFromDTO(setting, dto)
	setting.UpdatedAt = time.Now()

	// Güncelle
	err = s.settingRepo.UpdateSettings(setting)
	if err != nil {
		return nil, err
	}

	return s.toResponseDTO(setting), nil
}

func (s *settingService) toResponseDTO(setting *entities.Setting) *dtos.SettingResponseDTO {
	return &dtos.SettingResponseDTO{
		ID:                  setting.ID,
		CompanyName:         setting.CompanyName,
		CompanyAddress:      setting.CompanyAddress,
		CompanyPhone:        setting.CompanyPhone,
		CompanyEmail:        setting.CompanyEmail,
		TaxNumber:           setting.TaxNumber,
		CurrencySymbol:      setting.CurrencySymbol,
		DefaultTaxRate:      setting.DefaultTaxRate,
		LowStockThreshold:   setting.LowStockThreshold,
		EnableNotifications: setting.EnableNotifications,
		EnableAutoBackup:    setting.EnableAutoBackup,
		BackupFrequency:     setting.BackupFrequency,
		Theme:               setting.Theme,
		CreatedAt:           setting.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:           setting.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

func (s *settingService) updateEntityFromDTO(setting *entities.Setting, dto *dtos.SettingUpdateDTO) {
	setting.CompanyName = dto.CompanyName
	setting.CompanyAddress = dto.CompanyAddress
	setting.CompanyPhone = dto.CompanyPhone
	setting.CompanyEmail = dto.CompanyEmail
	setting.TaxNumber = dto.TaxNumber
	setting.CurrencySymbol = dto.CurrencySymbol
	setting.DefaultTaxRate = dto.DefaultTaxRate
	setting.LowStockThreshold = dto.LowStockThreshold
	setting.EnableNotifications = dto.EnableNotifications
	setting.EnableAutoBackup = dto.EnableAutoBackup
	setting.BackupFrequency = dto.BackupFrequency
	setting.Theme = dto.Theme
}
