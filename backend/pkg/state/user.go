package state

import (
	"context"

	"github.com/google/uuid"
)

const (
	CurrentUserID                = "CurrentUserID"
	CurrentUserOrganizationID    = "CurrentUserOrganizationID"
	CurrentUserSubOrganizationID = "CurrentUserSubOrganizationID"
)

func CurrentUser(c context.Context) uuid.UUID {
	value := c.Value(CurrentUserID)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func CurrentUserOrganization(c context.Context) uuid.UUID {
	value := c.Value(CurrentUserOrganizationID)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func CurrentUserSubOrganization(c context.Context) uuid.UUID {
	value := c.Value(CurrentUserSubOrganizationID)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}
