package middleware

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/state"
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		c.Set("CurrentIP", clientIP)
		c.Set(state.CurrentUserIP, clientIP)

		// Update context with real IP
		ctx := context.WithValue(c.Request.Context(), state.CurrentUserIP, clientIP)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

type Claims struct {
	UserID            string `json:"user_id"`
	Username          string `json:"username"`
	Role              string `json:"role"`
	OrganizationID    string `json:"organization_id"`
	SubOrganizationID string `json:"sub_organization_id"`

	// State package fields
	AdminID      string `json:"admin_id,omitempty"`
	AdminToken   string `json:"admin_token,omitempty"`
	DepartmentID string `json:"department_id,omitempty"`
	UserIP       string `json:"user_ip,omitempty"`
	AdminAgent   string `json:"admin_agent,omitempty"`
	IsMainOrg    bool   `json:"is_main_org,omitempty"`
	IsAuthorized bool   `json:"is_authorized,omitempty"`
	ApiKey       string `json:"api_key,omitempty"`
	ApiSecret    string `json:"api_secret,omitempty"`
	InternalAuth bool   `json:"internal_auth,omitempty"`

	jwt.RegisteredClaims
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
			c.Abort()
			return
		}

		claims := &Claims{}
		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(config.ReadValue().JwtSecret), nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("organization_id", claims.OrganizationID)
		c.Set("sub_organization_id", claims.SubOrganizationID)

		// Add all state values to context
		ctx := c.Request.Context()

		// Basic user info
		ctx = context.WithValue(ctx, "organization_id", claims.OrganizationID)
		ctx = context.WithValue(ctx, "sub_organization_id", claims.SubOrganizationID)
		ctx = context.WithValue(ctx, state.CurrentUserID, claims.UserID)
		ctx = context.WithValue(ctx, state.CurrentUserOrganizationID, claims.OrganizationID)
		ctx = context.WithValue(ctx, state.CurrentUserSubOrganizationID, claims.SubOrganizationID)

		// Debug log
		fmt.Printf("DEBUG: Setting organization_id in context: %s\n", claims.OrganizationID)

		// Admin state
		if claims.AdminID != "" {
			ctx = context.WithValue(ctx, state.CurrentAdminId, claims.AdminID)
		}
		if claims.AdminToken != "" {
			ctx = context.WithValue(ctx, state.CurrentAdminToken, claims.AdminToken)
		}
		if claims.DepartmentID != "" {
			ctx = context.WithValue(ctx, state.CurrentDepartmentId, claims.DepartmentID)
		}
		ctx = context.WithValue(ctx, state.MainOrg, claims.IsMainOrg)

		// IP and Agent info (will be updated by ClaimIp middleware)
		if claims.UserIP != "" {
			ctx = context.WithValue(ctx, state.CurrentUserIP, claims.UserIP)
		}
		if claims.AdminAgent != "" {
			ctx = context.WithValue(ctx, state.CurrentAdminAgentCTX, claims.AdminAgent)
		}

		// API credentials
		if claims.ApiKey != "" {
			ctx = context.WithValue(ctx, state.ApiKey, claims.ApiKey)
		}
		if claims.ApiSecret != "" {
			ctx = context.WithValue(ctx, state.ApiSecret, claims.ApiSecret)
		}

		// Internal auth
		ctx = context.WithValue(ctx, state.Auth, claims.InternalAuth)

		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists || role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}
		c.Next()
	}
}
