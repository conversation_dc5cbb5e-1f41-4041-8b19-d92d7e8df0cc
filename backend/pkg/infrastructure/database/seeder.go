package database

/*
	func seedUsers(db *gorm.DB) {
		log.Println("🔧 Seeding users...")

		users := []entities.User{
			{Username: "admin", Password: "admin123"},
		}

		for _, user := range users {
			if err := db.Create(&user).Error; err != nil {
				log.Printf("❌ Error creating user '%s': %v", user.Username, err)
			} else {
				log.Printf("✅ Created user: %s", user.Username)
			}
		}
	}

	func seedCampaigns(db *gorm.DB) {
		log.Println("🔧 Seeding campaigns...")

		campaigns := []entities.Campaign{
			{
				Name:        "<PERSON><PERSON>",
				Description: "Tüm beyaz eşyalarda %20 indirim",
				StartDate:   "2024-06-01",
				EndDate:     "2024-08-31",
			},
			{
				Name:        "<PERSON>ı<PERSON>any<PERSON>",
				Description: "Isıtıcı ve kombi ürünlerinde özel fiyatlar",
				StartDate:   "2024-12-01",
				EndDate:     "2025-02-28",
			},
			{
				Name:        "Bahar Temizliği",
				Description: "Temizlik ürünlerinde büyük indirimler",
				StartDate:   "2024-03-01",
				EndDate:     "2024-05-31",
			},
			{
				Name:        "Teknoloji Haftası",
				Description: "TV ve elektronik ürünlerde özel kampanya",
				StartDate:   "2024-09-01",
				EndDate:     "2024-09-30",
			},
		}

		for _, campaign := range campaigns {
			if err := db.Create(&campaign).Error; err != nil {
				log.Printf("❌ Error creating campaign '%s': %v", campaign.Name, err)
			} else {
				log.Printf("✅ Created campaign: %s", campaign.Name)
			}
		}
	}

	func seedProducts(db *gorm.DB) {
		log.Println("🔧 Seeding products...")
		var categories []entities.Category
		db.Find(&categories)

		if len(categories) == 0 {
			log.Println("❌ No categories found, cannot create products")
			return
		}

		// Get campaigns
		var campaigns []entities.Campaign
		db.Find(&campaigns)

		log.Printf("Found %d campaigns for product seeding", len(campaigns))

		products := []struct {
			Name         string
			ProductCode  string
			Price        float32
			Quantity     int
			CategoryName string
			CampaignID   int
		}{
			{"Samsung Buzdolabı RT50K6000S8", "SAM-BUZ-001", 15999.99, 5, "Buzdolabı", 0},
			{"Bosch KGN86AIF0N", "BSH-BUZ-002", 22999.99, 3, "Buzdolabı", 0},
			{"Arçelik 2480 NEIY", "ARC-BUZ-003", 12499.99, 8, "Buzdolabı", 1},
			{"Vestel NF480 E", "VST-BUZ-004", 11999.99, 6, "Buzdolabı", 0},

			{"Beko RFNE312E23W", "BEK-DRN-001", 8999.99, 4, "Derin Dondurucu", 0},
			{"Arçelik 5330 NEFY", "ARC-DRN-002", 9499.99, 3, "Derin Dondurucu", 0},

			{"Bosch WAT28461TR", "BSH-CAM-001", 7999.99, 7, "Çamaşır Makinesi", 1},
			{"Samsung WW90T4040CE", "SAM-CAM-002", 6999.99, 5, "Çamaşır Makinesi", 0},
			{"Arçelik 9103 YPS", "ARC-CAM-003", 5999.99, 10, "Çamaşır Makinesi", 1},

			{"Bosch WTW85461TR", "BSH-KUR-001", 12999.99, 3, "Kurutma Makinesi", 0},
			{"Samsung DV80T5220AW", "SAM-KUR-002", 8999.99, 4, "Kurutma Makinesi", 0},

			{"Bosch SMS25AW00T", "BSH-BUL-001", 4999.99, 6, "Bulaşık Makinesi", 1},
			{"Arçelik 6232 I", "ARC-BUL-002", 3999.99, 8, "Bulaşık Makinesi", 0},

			{"Bosch HBG635BS1", "BSH-FIR-001", 8999.99, 4, "Fırın", 0},
			{"Samsung NV75K5571RS", "SAM-FIR-002", 7499.99, 5, "Fırın", 0},

			{"Samsung UE55AU7000", "SAM-TV-001", 12999.99, 8, "Televizyon", 4},
			{"LG 55NANO756PA", "LG-TV-002", 14999.99, 6, "Televizyon", 4},
			{"Sony KD-55X80J", "SNY-TV-003", 16999.99, 4, "Televizyon", 4},

			{"Daikin FTXM35R", "12000 BTU Inverter", 8999.99, 10, "Klima", 0},
			{"Mitsubishi MSZ-LN25VG", "9000 BTU Inverter", 7999.99, 12, "Klima", 0},

			{"Vaillant ecoTEC plus", "24 kW Kombi", 4999.99, 6, "Kombi", 2},
			{"Baymak Duotec", "28 kW Kombi", 5499.99, 5, "Kombi", 2},
		}

		for _, p := range products {
			// Find category ID by name
			var categoryID uuid.UUID
			for _, cat := range categories {
				if cat.Name == p.CategoryName {
					categoryID = cat.ID
					break
				}
			}

			if categoryID == uuid.Nil {
				log.Printf("❌ Category not found for product: %s", p.Name)
				continue
			}

			// Set campaign ID if available
			var campaignID uuid.UUID
			if p.CampaignID > 0 && p.CampaignID <= len(campaigns) {
				// Use the actual campaign UUID (1-based index in seeder data)
				campaignID = campaigns[p.CampaignID-1].ID
				log.Printf("Assigning campaign '%s' to product '%s'", campaigns[p.CampaignID-1].Name, p.Name)
			}

			product := entities.Product{
				Name:        p.Name,
				ProductCode: p.ProductCode,
				Price:       p.Price,
				Quantity:    p.Quantity,
				CategoryID:  categoryID,
				CampaignID:  campaignID,
			}

			if err := db.Create(&product).Error; err != nil {
				log.Printf("❌ Error creating product '%s': %v", p.Name, err)
			} else {
				log.Printf("✅ Created product: %s", p.Name)
			}
		}
	}

	func seedSafes(db *gorm.DB) {
		log.Println("🔧 Seeding safes...")

		safes := []entities.Safe{
			{Amount: 50000.00},
			{Amount: 25000.00},
			{Amount: 75000.00},
		}

		for i, safe := range safes {
			if err := db.Create(&safe).Error; err != nil {
				log.Printf("❌ Error creating safe %d: %v", i+1, err)
			} else {
				log.Printf("✅ Created safe with amount: %.2f TL", safe.Amount)
			}
		}
	}

	func seedDebts(db *gorm.DB) {
		log.Println("🔧 Seeding debts...")

		debts := []entities.Debt{
			{Name: "Ahmet", Surname: "Yılmaz", Phone: "05551234567", Amount: 2500.00, IsPaid: false},
			{Name: "Fatma", Surname: "Kaya", Phone: "05552345678", Amount: 1800.00, IsPaid: false},
			{Name: "Mehmet", Surname: "Demir", Phone: "05553456789", Amount: 3200.00, IsPaid: true},
			{Name: "Ayşe", Surname: "Şahin", Phone: "05554567890", Amount: 950.00, IsPaid: false},
			{Name: "Ali", Surname: "Özkan", Phone: "05555678901", Amount: 4100.00, IsPaid: false},
			{Name: "Zeynep", Surname: "Arslan", Phone: "05556789012", Amount: 1200.00, IsPaid: true},
			{Name: "Mustafa", Surname: "Koç", Phone: "05557890123", Amount: 2800.00, IsPaid: false},
			{Name: "Elif", Surname: "Yıldız", Phone: "05558901234", Amount: 1500.00, IsPaid: false},
		}

		for _, debt := range debts {
			if err := db.Create(&debt).Error; err != nil {
				log.Printf("❌ Error creating debt for '%s %s': %v", debt.Name, debt.Surname, err)
			} else {
				log.Printf("✅ Created debt: %s %s - %.2f TL", debt.Name, debt.Surname, debt.Amount)
			}
		}
	}

	func seedSales(db *gorm.DB) {
		log.Println("🔧 Seeding sales...")

		// Get products first
		var products []entities.Product
		db.Find(&products)

		if len(products) == 0 {
			log.Println("❌ No products found, cannot create sales")
			return
		}

		customers := []struct {
			Name  string
			Phone string
			TC    string
		}{
			{"Mehmet Özdemir", "05551111111", "12345678901"},
			{"Ayşe Yılmaz", "05552222222", "23456789012"},
			{"Ali Kaya", "05553333333", "34567890123"},
			{"Fatma Demir", "05554444444", "45678901234"},
			{"Ahmet Şahin", "05555555555", "56789012345"},
			{"Zeynep Özkan", "05556666666", "67890123456"},
			{"Mustafa Arslan", "05557777777", "78901234567"},
			{"Elif Koç", "05558888888", "89012345678"},
		}

		// Create 15-20 sales records
		for i := 0; i < 18; i++ {
			// Random product
			product := products[rand.Intn(len(products))]

			// Random customer
			customer := customers[rand.Intn(len(customers))]

			// Random quantity (1-3)
			quantity := rand.Intn(3) + 1

			// Calculate prices
			unitPrice := product.Price
			totalPrice := unitPrice * float32(quantity)

			// Random date in last 30 days
			daysAgo := rand.Intn(30)
			saleDate := time.Now().AddDate(0, 0, -daysAgo).Format("2006-01-02")

			sale := entities.Sale{
				ProductID:     product.ID,
				Quantity:      quantity,
				UnitPrice:     unitPrice,
				TotalPrice:    totalPrice,
				CustomerName:  customer.Name,
				CustomerPhone: customer.Phone,
				CustomerTC:    customer.TC,
				SaleDate:      saleDate,
			}

			if err := db.Create(&sale).Error; err != nil {
				log.Printf("❌ Error creating sale: %v", err)
			} else {
				log.Printf("✅ Created sale: %s - %s (%.2f TL)", product.Name, customer.Name, totalPrice)
			}
		}
	}

func seedDefaultOrganizationAndAdmin(db *gorm.DB) {
	log.Println("🔧 Seeding default organization and admin user...")

	// First, create default organization
	var existingOrg entities.Organization
	err := db.Where("name = ?", "Default Organization").First(&existingOrg).Error

	var defaultOrgID string
	if err != nil {
		// Create default organization
		defaultOrg := entities.Organization{
			Name:        "Default Organization",
			Description: "Default organization for existing data",
			IsActive:    true,
			IsMain:      true,
		}

		if err := db.Create(&defaultOrg).Error; err != nil {
			log.Printf("❌ Error creating default organization: %v", err)
			return
		}
		log.Printf("✅ Created default organization: %s", defaultOrg.Name)
		defaultOrgID = defaultOrg.ID.String()
	} else {
		log.Println("✅ Default organization already exists")
		// Ensure it's marked as main organization
		if !existingOrg.IsMain {
			existingOrg.IsMain = true
			if err := db.Save(&existingOrg).Error; err != nil {
				log.Printf("❌ Error updating default organization: %v", err)
			} else {
				log.Println("✅ Updated default organization to main organization")
			}
		}
		defaultOrgID = existingOrg.ID.String()
	}

	// Check if admin user already exists
	var existingAdmin entities.User
	err = db.Where("username = ?", "admin").First(&existingAdmin).Error
	if err == nil {
		// Update existing admin with organization_id if not set
		if existingAdmin.OrganizationID.String() == "00000000-0000-0000-0000-000000000000" {
			existingAdmin.OrganizationID = existingOrg.ID
			if updateErr := db.Save(&existingAdmin).Error; updateErr != nil {
				log.Printf("❌ Error updating admin user with organization: %v", updateErr)
			} else {
				log.Println("✅ Updated admin user with default organization")
			}
		}
		return
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("❌ Error hashing admin password: %v", err)
		return
	}

	// Create admin user with organization
	admin := entities.User{
		Username:       "admin",
		Password:       string(hashedPassword),
		Role:           "admin",
		IsActive:       true,
		OrganizationID: existingOrg.ID,
	}

	if err := db.Create(&admin).Error; err != nil {
		log.Printf("❌ Error creating admin user: %v", err)
	} else {
		log.Printf("✅ Created admin user: %s with organization: %s", admin.Username, defaultOrgID)
	}
}

func migrateExistingDataToDefaultOrganization(db *gorm.DB) {
	log.Println("🔧 Migrating existing data to default organization...")

	// Get default organization
	var defaultOrg entities.Organization
	err := db.Where("name = ?", "Default Organization").First(&defaultOrg).Error
	if err != nil {
		log.Printf("❌ Default organization not found: %v", err)
		return
	}

	// Migrate Users
	result := db.Model(&entities.User{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating users: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d users to default organization", result.RowsAffected)
	}

	// Migrate Products
	result = db.Model(&entities.Product{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating products: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d products to default organization", result.RowsAffected)
	}

	// Migrate Categories
	result = db.Model(&entities.Category{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating categories: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d categories to default organization", result.RowsAffected)
	}

	// Migrate Campaigns
	result = db.Model(&entities.Campaign{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating campaigns: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d campaigns to default organization", result.RowsAffected)
	}

	// Migrate Customers
	result = db.Model(&entities.Customer{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating customers: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d customers to default organization", result.RowsAffected)
	}

	// Migrate Debts
	result = db.Model(&entities.Debt{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating debts: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d debts to default organization", result.RowsAffected)
	}

	// Migrate Sales
	result = db.Model(&entities.Sale{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating sales: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d sales to default organization", result.RowsAffected)
	}

	// Migrate Safes
	result = db.Model(&entities.Safe{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating safes: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d safes to default organization", result.RowsAffected)
	}

	// Migrate Settings
	result = db.Model(&entities.Setting{}).Where("organization_id = ? OR organization_id IS NULL", "00000000-0000-0000-0000-000000000000").Update("organization_id", defaultOrg.ID)
	if result.Error != nil {
		log.Printf("❌ Error migrating settings: %v", result.Error)
	} else {
		log.Printf("✅ Migrated %d settings to default organization", result.RowsAffected)
	}

	log.Println("✅ Data migration to default organization completed!")
}

*/
