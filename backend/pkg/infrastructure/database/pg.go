package database

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/entities"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db  *gorm.DB
	err error
)

func InitDB(host, port, user, password, dbname string) {
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s application_name=db_connection", host, port, user, password, dbname, config.ReadValue().Database.SslMode)

	maxRetries := 10
	baseDelay := time.Second

	for i := 0; i < maxRetries; i++ {
		db, err = gorm.Open(
			postgres.New(
				postgres.Config{
					DSN:                  dsn,
					PreferSimpleProtocol: true,
				},
			),
			&gorm.Config{
				TranslateError: true,
			},
		)

		if err == nil {
			sqldb, testErr := db.DB()
			if testErr == nil {
				if pingErr := sqldb.Ping(); pingErr == nil {
					break
				}
			}
		}

		if i == maxRetries-1 {
			log.Printf("❌ Failed to connect to database after %d attempts", maxRetries)
			panic(err)
		}

		delay := baseDelay * time.Duration(1<<uint(i)) // Exponential backoff
		if delay > 30*time.Second {
			delay = 30 * time.Second // Cap at 30 seconds
		}

		log.Printf("🔄 Database connection attempt %d/%d failed, retrying in %v... Error: %v", i+1, maxRetries, delay, err)
		time.Sleep(delay)
	}

	sqldb, err := db.DB()
	if err != nil {
		panic("Failed to connect database, error: " + err.Error())
	}
	sqldb.SetMaxIdleConns(3)
	sqldb.SetMaxOpenConns(90)
	sqldb.SetConnMaxLifetime(time.Hour)
	sqldb.SetConnMaxIdleTime(time.Second * 2)

	db.AutoMigrate(
		&entities.Organization{},
		&entities.User{},
		&entities.Product{},
		&entities.ProductStock{},
		&entities.Category{},
		&entities.Campaign{},
		&entities.Debt{},
		&entities.Safe{},
		&entities.Sale{},
		&entities.Customer{},
		&entities.Setting{},
	)

	db.Exec("SET TIME ZONE 'Europe/Istanbul';")

	// seedDefaultOrganizationAndAdmin(db)

	// migrateExistingDataToDefaultOrganization(db)

	// Seed default categories
	// seedDefaultCategories(db)

	// Seed dummy data for development
	// seedDummyData(db)
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}

/*
func seedDefaultCategories(db *gorm.DB) {
	defaultCategories := []string{
		"Buzdolabı",
		"Derin Dondurucu",
		"Çamaşır Makinesi",
		"Kurutma Makinesi",
		"Bulaşık Makinesi",
		"Fırın",
		"Ankastre Set",
		"Mikrodalga Fırın",
		"Set Üstü Ocak",
		"Su Isıtıcı (Kettle)",
		"Elektrikli Süpürge",
		"Ütü",
		"Televizyon",
		"Klima",
		"Şofben",
		"Kombi",
		"Şarap Soğutucu",
		"Mini Buzdolabı",
		"Ekmek Kızartma Makinesi",
		"Kahve Makinesi",
	}

	for _, categoryName := range defaultCategories {
		var existingCategory entities.Category
		result := db.Where("name = ? AND (deleted_at IS NULL OR deleted_at = '0001-01-01 00:00:00+00')", categoryName).First(&existingCategory)

		// If category doesn't exist, create it
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				category := entities.Category{
					Name: categoryName,
				}

				if err := db.Create(&category).Error; err != nil {
					log.Printf("❌ Error creating default category '%s': %v", categoryName, err)
				} else {
					log.Printf("✅ Default category created: %s (ID: %s)", categoryName, category.ID.String())
				}
			} else {
				log.Printf("❌ Error checking for category '%s': %v", categoryName, result.Error)
			}
		} else {
			log.Printf("⏭️  Category already exists: %s", categoryName)
		}
	}

	// Verify the seeding worked
	var count int64
	db.Model(&entities.Category{}).Where("deleted_at IS NULL OR deleted_at = '0001-01-01 00:00:00+00'").Count(&count)
	log.Printf("✅ Default categories seeding completed. Total categories in database: %d", count)
}

func seedDummyData(db *gorm.DB) {
	log.Println("🌱 Starting dummy data seeding...")

	// Check if dummy data already exists
	var userCount int64
	db.Model(&entities.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("⏭️  Dummy data already exists, skipping seeding")
		return
	}

	// seedUsers(db)
	// seedCampaigns(db)
	// seedProducts(db)
	// seedSafes(db)
	// seedDebts(db)
	// seedSales(db)

	log.Println("✅ Dummy data seeding completed!")
}
*/
