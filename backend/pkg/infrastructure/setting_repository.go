package infrastructure

import (
	"business-mamagement/pkg/domain"
	"business-mamagement/pkg/entities"

	"gorm.io/gorm"
)

type settingRepository struct {
	db *gorm.DB
}

func NewSettingRepository(db *gorm.DB) domain.SettingRepository {
	return &settingRepository{db: db}
}

func (r *settingRepository) GetSettings() (*entities.Setting, error) {
	var setting entities.Setting

	// İlk ayar kaydını getir, yoksa varsayılan değerlerle oluştur
	err := r.db.First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Varsayılan ayarları oluştur
			defaultSetting := &entities.Setting{
				CompanyName:         "",
				CompanyAddress:      "",
				CompanyPhone:        "",
				CompanyEmail:        "",
				TaxNumber:           "",
				CurrencySymbol:      "TL",
				DefaultTaxRate:      18,
				LowStockThreshold:   10,
				EnableNotifications: true,
				EnableAutoBackup:    false,
				BackupFrequency:     "daily",
				Theme:               "light",
			}

			createErr := r.CreateSettings(defaultSetting)
			if createErr != nil {
				return nil, createErr
			}
			return defaultSetting, nil
		}
		return nil, err
	}

	return &setting, nil
}

func (r *settingRepository) CreateSettings(setting *entities.Setting) error {
	return r.db.Create(setting).Error
}

func (r *settingRepository) UpdateSettings(setting *entities.Setting) error {
	return r.db.Save(setting).Error
}
