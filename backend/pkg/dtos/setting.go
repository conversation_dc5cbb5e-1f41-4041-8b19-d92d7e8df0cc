package dtos

type SettingCreateDTO struct {
	CompanyName         string  `json:"company_name" validate:"required"`
	CompanyAddress      string  `json:"company_address"`
	CompanyPhone        string  `json:"company_phone"`
	CompanyEmail        string  `json:"company_email" validate:"email"`
	TaxNumber           string  `json:"tax_number"`
	CurrencySymbol      string  `json:"currency_symbol" validate:"required"`
	DefaultTaxRate      float64 `json:"default_tax_rate" validate:"min=0,max=100"`
	LowStockThreshold   int     `json:"low_stock_threshold" validate:"min=0"`
	EnableNotifications bool    `json:"enable_notifications"`
	EnableAutoBackup    bool    `json:"enable_auto_backup"`
	BackupFrequency     string  `json:"backup_frequency" validate:"oneof=daily weekly monthly"`
	Theme               string  `json:"theme" validate:"oneof=light dark auto"`
}

type SettingUpdateDTO struct {
	CompanyName         string  `json:"company_name"`
	CompanyAddress      string  `json:"company_address"`
	CompanyPhone        string  `json:"company_phone"`
	CompanyEmail        string  `json:"company_email" validate:"omitempty,email"`
	TaxNumber           string  `json:"tax_number"`
	CurrencySymbol      string  `json:"currency_symbol"`
	DefaultTaxRate      float64 `json:"default_tax_rate" validate:"min=0,max=100"`
	LowStockThreshold   int     `json:"low_stock_threshold" validate:"min=0"`
	EnableNotifications bool    `json:"enable_notifications"`
	EnableAutoBackup    bool    `json:"enable_auto_backup"`
	BackupFrequency     string  `json:"backup_frequency" validate:"oneof=daily weekly monthly"`
	Theme               string  `json:"theme" validate:"oneof=light dark auto"`
}

type SettingResponseDTO struct {
	ID                  uint    `json:"id"`
	CompanyName         string  `json:"company_name"`
	CompanyAddress      string  `json:"company_address"`
	CompanyPhone        string  `json:"company_phone"`
	CompanyEmail        string  `json:"company_email"`
	TaxNumber           string  `json:"tax_number"`
	CurrencySymbol      string  `json:"currency_symbol"`
	DefaultTaxRate      float64 `json:"default_tax_rate"`
	LowStockThreshold   int     `json:"low_stock_threshold"`
	EnableNotifications bool    `json:"enable_notifications"`
	EnableAutoBackup    bool    `json:"enable_auto_backup"`
	BackupFrequency     string  `json:"backup_frequency"`
	Theme               string  `json:"theme"`
	CreatedAt           string  `json:"created_at"`
	UpdatedAt           string  `json:"updated_at"`
}
